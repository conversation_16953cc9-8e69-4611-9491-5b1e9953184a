// AppointmentContext.js - Context for appointment data
import { createContext, useContext } from 'react';
import useAppointmentController from '../controllers/AppointmentController';

// Create the context
const AppointmentContext = createContext();

// Create a provider component
export const AppointmentProvider = ({ children }) => {
  const appointmentController = useAppointmentController();

  return (
    <AppointmentContext.Provider value={appointmentController}>
      {children}
    </AppointmentContext.Provider>
  );
};

// Custom hook to use the context
export const useAppointment = () => {
  const context = useContext(AppointmentContext);
  if (!context) {
    throw new Error('useAppointment must be used within an AppointmentProvider');
  }
  return context;
};

export default AppointmentContext;
