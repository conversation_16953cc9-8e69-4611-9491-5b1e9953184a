// AppointmentModel.js - Model for appointment data
import { useState, useEffect } from 'react';

// Mock data for therapists
export const therapists = [
  {
    id: 1,
    name: 'Dr. <PERSON>',
    specialty: 'Anxiety & Depression',
    experience: '10 years',
    rating: 4.9,
    image: '👩‍⚕️',
    available: true
  },
  {
    id: 2,
    name: 'Dr. <PERSON>',
    specialty: 'Stress Management',
    experience: '8 years',
    rating: 4.7,
    image: '👨‍⚕️',
    available: true
  },
  {
    id: 3,
    name: 'Dr. <PERSON>',
    specialty: 'Trauma & PTSD',
    experience: '12 years',
    rating: 4.8,
    image: '👩‍⚕️',
    available: true
  },
  {
    id: 4,
    name: 'Dr. <PERSON>',
    specialty: 'Cognitive Behavioral Therapy',
    experience: '15 years',
    rating: 4.9,
    image: '👨‍⚕️',
    available: true
  }
];

// Generate dates for the next 14 days
export const getDates = () => {
  const dates = [];
  const today = new Date();

  for (let i = 0; i < 14; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    dates.push(date);
  }

  return dates;
};

// Generate time slots
export const getTimeSlots = () => {
  return [
    '9:00 AM', '10:00 AM', '11:00 AM',
    '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM'
  ];
};

// Format date for display
export const formatDate = (date) => {
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  return {
    day: days[date.getDay()],
    date: date.getDate(),
    month: months[date.getMonth()],
    year: date.getFullYear(),
    fullDate: `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`
  };
};

// Format date string for storage
export const formatDateString = (date) => {
  return date.toISOString().split('T')[0];
};

// Appointment Model Hook
export const useAppointmentModel = () => {
  const [appointments, setAppointments] = useState([]);
  const [upcomingAppointments, setUpcomingAppointments] = useState([]);
  const [pastAppointments, setPastAppointments] = useState([]);

  // Load appointments from storage (mock implementation)
  useEffect(() => {
    // In a real app, this would load from AsyncStorage or an API
    const mockAppointments = [
      {
        id: '1',
        therapistId: 1,
        date: '2023-06-15',
        time: '10:00 AM',
        status: 'completed',
        notes: 'Initial consultation'
      },
      {
        id: '2',
        therapistId: 2,
        date: '2023-07-20',
        time: '2:00 PM',
        status: 'completed',
        notes: 'Follow-up session'
      },
      {
        id: '3',
        therapistId: 3,
        date: new Date(new Date().setDate(new Date().getDate() + 3)).toISOString().split('T')[0],
        time: '11:00 AM',
        status: 'scheduled',
        notes: 'Discuss progress'
      }
    ];
    
    setAppointments(mockAppointments);
    
    // Filter appointments into upcoming and past
    const now = new Date();
    const upcoming = [];
    const past = [];
    
    mockAppointments.forEach(appointment => {
      const appointmentDate = new Date(`${appointment.date}T${appointment.time}`);
      if (appointmentDate > now) {
        upcoming.push(appointment);
      } else {
        past.push(appointment);
      }
    });
    
    setUpcomingAppointments(upcoming);
    setPastAppointments(past);
  }, []);

  // Add a new appointment
  const addAppointment = (appointment) => {
    const newAppointment = {
      id: Date.now().toString(),
      ...appointment,
      status: 'scheduled'
    };
    
    setAppointments(prev => [...prev, newAppointment]);
    setUpcomingAppointments(prev => [...prev, newAppointment]);
    
    return newAppointment;
  };

  // Cancel an appointment
  const cancelAppointment = (appointmentId) => {
    const updatedAppointments = appointments.map(appointment => 
      appointment.id === appointmentId 
        ? { ...appointment, status: 'cancelled' } 
        : appointment
    );
    
    setAppointments(updatedAppointments);
    
    // Update upcoming appointments
    setUpcomingAppointments(
      updatedAppointments.filter(
        appointment => 
          appointment.status === 'scheduled' && 
          new Date(`${appointment.date}T${appointment.time}`) > new Date()
      )
    );
    
    return updatedAppointments.find(a => a.id === appointmentId);
  };

  // Reschedule an appointment
  const rescheduleAppointment = (appointmentId, newDate, newTime) => {
    const updatedAppointments = appointments.map(appointment => 
      appointment.id === appointmentId 
        ? { ...appointment, date: newDate, time: newTime } 
        : appointment
    );
    
    setAppointments(updatedAppointments);
    
    // Update upcoming appointments
    const now = new Date();
    setUpcomingAppointments(
      updatedAppointments.filter(
        appointment => 
          appointment.status === 'scheduled' && 
          new Date(`${appointment.date}T${appointment.time}`) > now
      )
    );
    
    return updatedAppointments.find(a => a.id === appointmentId);
  };

  return {
    appointments,
    upcomingAppointments,
    pastAppointments,
    addAppointment,
    cancelAppointment,
    rescheduleAppointment
  };
};

export default useAppointmentModel;
