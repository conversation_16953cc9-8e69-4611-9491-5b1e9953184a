import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import BottomNavigation from './components/BottomNavigation';

const AssessmentResults = () => {
  const router = useRouter();
  const { id, results: resultsParam } = useLocalSearchParams();
  const [results, setResults] = useState(null);
  const [assessmentTitle, setAssessmentTitle] = useState('');

  useEffect(() => {
    if (resultsParam) {
      try {
        const parsedResults = JSON.parse(resultsParam);
        setResults(parsedResults);
      } catch (error) {
        console.error('Error parsing results:', error);
      }
    }

    // Set assessment title based on ID
    if (id === 'dass') {
      setAssessmentTitle('DASS (Depression Anxiety Stress Scales)');
    } else if (id === 'pss') {
      setAssessmentTitle('PSS (Perceived Stress Scale)');
    } else if (id === 'gad7') {
      setAssessmentTitle('GAD-7 (Generalized Anxiety Disorder)');
    } else if (id === 'phq9') {
      setAssessmentTitle('PHQ-9 (Patient Health Questionnaire)');
    }
  }, [resultsParam, id]);

  const getLevelColor = (level) => {
    switch (level) {
      case 'Normal':
        return '#6B9142'; // Green
      case 'Mild':
        return '#A3C677'; // Light green
      case 'Moderate':
        return '#FFC107'; // Yellow
      case 'Severe':
        return '#FF9800'; // Orange
      case 'Extreme':
        return '#F44336'; // Red
      default:
        return '#6B9142';
    }
  };

  const getRecommendations = (category, level) => {
    const recommendations = {
      depression: {
        Normal: [
          'Continue with your current healthy habits',
          'Maintain regular exercise and social connections',
          'Practice gratitude daily'
        ],
        Mild: [
          'Consider adding more physical activity to your routine',
          'Establish a regular sleep schedule',
          'Connect with friends and family regularly'
        ],
        Moderate: [
          'Consider speaking with a mental health professional',
          'Practice mindfulness meditation daily',
          'Establish a routine that includes enjoyable activities'
        ],
        Severe: [
          'Consult with a mental health professional',
          'Consider joining a support group',
          'Establish a self-care routine and stick to it'
        ],
        Extreme: [
          'Seek professional help immediately',
          'Contact a crisis helpline if needed',
          'Reach out to trusted friends or family for support'
        ]
      },
      anxiety: {
        Normal: [
          'Continue with your current healthy habits',
          'Practice regular relaxation techniques',
          'Maintain a balanced lifestyle'
        ],
        Mild: [
          'Try deep breathing exercises when feeling anxious',
          'Limit caffeine and alcohol consumption',
          'Practice progressive muscle relaxation'
        ],
        Moderate: [
          'Consider speaking with a mental health professional',
          'Practice daily mindfulness meditation',
          'Identify and challenge anxious thoughts'
        ],
        Severe: [
          'Consult with a mental health professional',
          'Learn and practice grounding techniques',
          'Establish a regular exercise routine'
        ],
        Extreme: [
          'Seek professional help immediately',
          'Contact a crisis helpline if needed',
          'Practice self-compassion and acceptance'
        ]
      },
      stress: {
        Normal: [
          'Continue with your current healthy habits',
          'Practice regular relaxation techniques',
          'Maintain work-life balance'
        ],
        Mild: [
          'Incorporate stress-reduction activities into your routine',
          'Practice time management techniques',
          'Ensure adequate sleep and nutrition'
        ],
        Moderate: [
          'Consider speaking with a mental health professional',
          'Practice daily stress management techniques',
          'Identify and address sources of stress'
        ],
        Severe: [
          'Consult with a mental health professional',
          'Prioritize self-care and stress reduction',
          'Consider lifestyle changes to reduce stress'
        ],
        Extreme: [
          'Seek professional help immediately',
          'Take immediate steps to reduce stressors',
          'Practice self-compassion and acceptance'
        ]
      }
    };

    return recommendations[category]?.[level] || [];
  };

  if (!results) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Loading results...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.push('/dashboard')} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Home</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Your Assessment Results</Text>
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.assessmentTitle}>{assessmentTitle}</Text>
        <Text style={styles.subtitle}>
          Answer each question honestly based on how you've been feeling over the past week
        </Text>

        {Object.entries(results).map(([category, data]) => (
          <View key={category} style={styles.resultCard}>
            <View style={styles.resultHeader}>
              <Text style={styles.categoryTitle}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Text>
              <View style={styles.scoreContainer}>
                <Text style={styles.scoreValue}>{data.score}</Text>
                <Text
                  style={[
                    styles.levelText,
                    { color: getLevelColor(data.level) }
                  ]}
                >
                  {data.level}
                </Text>
              </View>
            </View>

            {getRecommendations(category, data.level).length > 0 && (
              <View style={styles.recommendationsContainer}>
                <Text style={styles.recommendationsTitle}>Recommendations:</Text>
                {getRecommendations(category, data.level).map((rec, index) => (
                  <Text key={index} style={styles.recommendationItem}>• {rec}</Text>
                ))}
              </View>
            )}
          </View>
        ))}

        <View style={styles.disclaimerContainer}>
          <Text style={styles.disclaimerText}>
            Disclaimer: This assessment is for self-evaluation purposes only and should not be considered a professional diagnosis. Please consult with a qualified healthcare professional for proper evaluation and treatment.
          </Text>
        </View>

        <TouchableOpacity
          style={styles.saveButton}
          onPress={() => router.push('/dashboard')}
        >
          <Text style={styles.saveButtonText}>Save Assessment</Text>
        </TouchableOpacity>
      </ScrollView>

      <BottomNavigation />
    </SafeAreaView>
  );
};

export default AssessmentResults;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#6B9142',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 30,
  },
  content: {
    padding: 20,
    paddingBottom: 80, // Space for bottom navigation
  },
  assessmentTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  resultCard: {
    backgroundColor: '#F5F9EE',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  scoreContainer: {
    alignItems: 'center',
  },
  scoreValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  levelText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  recommendationsContainer: {
    marginTop: 10,
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666666',
    marginBottom: 5,
  },
  recommendationItem: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 5,
    lineHeight: 20,
  },
  disclaimerContainer: {
    marginVertical: 20,
  },
  disclaimerText: {
    fontSize: 12,
    color: '#999999',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
