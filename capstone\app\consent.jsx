import { StyleSheet, Text, View, TouchableOpacity, SafeAreaView } from 'react-native';
import { useState } from 'react';
import { useRouter } from 'expo-router';

const Consent = () => {
  const router = useRouter();
  const [termsChecked, setTermsChecked] = useState(false);
  const [dataProcessingChecked, setDataProcessingChecked] = useState(false);

  const handleContinue = () => {
    if (termsChecked && dataProcessingChecked) {
      router.push('/create-account');
    } else {
      // You could add an alert or visual feedback here
      console.log('Please agree to all terms');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.logoContainer}>
        {/* Logo placeholder - you'll add the actual image later */}
        <View style={styles.logoPlaceholder}>
          <Text style={styles.logoText}>mentalease</Text>
        </View>
      </View>

      <Text style={styles.title}>Consent</Text>

      <View style={styles.infoBox}>
        <View style={styles.infoHeader}>
          <View style={styles.infoIcon}>
            <Text style={styles.infoIconText}>i</Text>
          </View>
          <Text style={styles.infoHeaderText}>Before we start</Text>
        </View>

        <Text style={styles.infoText}>
          We'll need your consent and preferences
        </Text>

        <View style={styles.checkboxContainer}>
          <TouchableOpacity
            style={styles.checkbox}
            onPress={() => setTermsChecked(!termsChecked)}
          >
            {termsChecked && <View style={styles.checkboxInner} />}
          </TouchableOpacity>
          <Text style={styles.checkboxLabel}>
            I agree to Mentalease's terms & conditions
          </Text>
        </View>

        <View style={styles.checkboxContainer}>
          <TouchableOpacity
            style={styles.checkbox}
            onPress={() => setDataProcessingChecked(!dataProcessingChecked)}
          >
            {dataProcessingChecked && <View style={styles.checkboxInner} />}
          </TouchableOpacity>
          <Text style={styles.checkboxLabel}>
            I agree to the processing of my personal & health data by Mentalease for the intended use of the app
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[
          styles.continueButton,
          (!termsChecked || !dataProcessingChecked) && styles.continueButtonDisabled
        ]}
        onPress={handleContinue}
      >
        <Text style={styles.continueButtonText}>Continue</Text>
        <Text style={styles.arrowIcon}>→</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default Consent;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 20,
  },
  logoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F5F9EE',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 30,
    textAlign: 'center',
  },
  infoBox: {
    backgroundColor: '#F5F9EE',
    borderRadius: 15,
    padding: 20,
    marginBottom: 30,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  infoIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#6B9142',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  infoIconText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  infoHeaderText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B9142',
  },
  infoText: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 20,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: '#6B9142',
    borderRadius: 4,
    marginRight: 10,
    marginTop: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxInner: {
    width: 16,
    height: 16,
    backgroundColor: '#6B9142',
    borderRadius: 2,
  },
  checkboxLabel: {
    flex: 1,
    fontSize: 14,
    color: '#666666',
  },
  continueButton: {
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
  },
  continueButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 10,
  },
  arrowIcon: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
});
