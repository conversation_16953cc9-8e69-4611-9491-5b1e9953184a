import { useEffect } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { useUser } from './context/UserContext';

const IndexRedirect = () => {
  const router = useRouter();
  const { userData } = useUser();

  useEffect(() => {
    // Check if user is logged in
    if (userData && userData.firstName) {
      // User is logged in, redirect to fixed dashboard
      router.replace('/dashboard-fixed');
    } else {
      // User is not logged in, show the welcome screen
      router.replace('/index');
    }
  }, []);

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" color="#6B9142" />
    </View>
  );
};

export default IndexRedirect;
