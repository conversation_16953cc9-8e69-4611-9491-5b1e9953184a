import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Image,
  Animated,
  Easing,
  Platform,
  Dimensions,
  BackHandler
} from 'react-native';
import { useRouter } from 'expo-router';
import { useUser } from './context/UserContext';
import { LinearGradient } from 'expo-linear-gradient';
import BottomNavigation from './components/BottomNavigation';
import Constants from 'expo-constants';
import HeaderCarousel from './components/HeaderCarousel';

// Inspirational quotes for the dashboard
const inspirationalQuotes = [
  "Mental health is not a destination, but a journey.",
  "You don't have to be positive all the time. It's perfectly okay to feel sad, angry, annoyed, frustrated, scared, or anxious.",
  "Your mental health is a priority. Your happiness is essential. Your self-care is a necessity.",
  "Recovery is not one and done. It is a lifelong journey that takes place one day, one step at a time.",
  "You are not alone in this journey.",
  "Self-care is how you take your power back.",
  "It's okay to not have it all figured out. That's how you grow.",
  "Be proud of yourself for how hard you're trying.",
  "Your present circumstances don't determine where you can go; they merely determine where you start."
];

const Dashboard = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const quoteOpacity = useRef(new Animated.Value(0)).current;

  const router = useRouter();
  const { userData, toggleHidePersonalInfo } = useUser();

  // Get screen dimensions for responsive design and update on dimension changes
  const [dimensions, setDimensions] = useState({
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
  });

  // Update dimensions when screen size changes (e.g., rotation)
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions({
        width: window.width,
        height: window.height,
      });
    });

    return () => subscription?.remove();
  }, []);

  // Check if user has completed personal information
  useEffect(() => {
    // Only redirect if user is not verified AND has no firstName
    if (!userData.emailVerified && !userData.firstName) {
      router.replace('/personal-information');
      return; // Exit early
    }

    // Prevent going back to personal information screen
    const preventBackNavigation = () => {
      return true; // Returning true prevents the back action
    };

    // Add event listener for hardware back button (Android)
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', preventBackNavigation);
    }

    // Start animations when component mounts
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.timing(quoteOpacity, {
        toValue: 1,
        duration: 1500,
        delay: 500,
        useNativeDriver: true,
      })
    ]).start();

    // Set a random quote
    const randomIndex = Math.floor(Math.random() * inspirationalQuotes.length);
    setQuote(inspirationalQuotes[randomIndex]);

    // Cleanup function
    return () => {
      if (Platform.OS === 'android') {
        BackHandler.removeEventListener('hardwareBackPress', preventBackNavigation);
      }
    };
  }, [userData.firstName]);

  const [quote, setQuote] = useState(inspirationalQuotes[0]);
  const moodOptions = ['😊', '🙂', '😐', '😔', '😫'];
  const moodLabels = ['Great', 'Good', 'Okay', 'Bad', 'Awful'];
  const [selectedMood, setSelectedMood] = useState(null);

  const handleMoodSelect = (mood, index) => {
    setSelectedMood(index);
    // Navigate to mood journal with the selected mood
    router.push({
      pathname: '/mood-journal',
      params: {
        mood: mood,
        moodIndex: index
      }
    });
  };

  const handleProfilePress = () => {
    router.push('/user-profile');
  };

  return (
    <View style={styles.container}>
      <StatusBar
        translucent={true}
        backgroundColor="transparent"
        barStyle="light-content"
      />

      <SafeAreaView style={styles.safeArea}>
        {/* Header with Carousel Background */}
        <View style={styles.headerGradient}>
          {/* Carousel Background */}
          <HeaderCarousel height={180} />

          {/* Header Content */}
          <View style={styles.headerContent}>
            <View style={styles.topBar}>
              <View style={styles.userInfoContainer}>
                <TouchableOpacity
                  style={styles.profilePicture}
                  onPress={handleProfilePress}
                >
                  {userData.profileImage ? (
                    <Image source={userData.profileImage} style={styles.profileImage} />
                  ) : (
                    <Text style={styles.profileInitial}>{userData.firstName.charAt(0)}</Text>
                  )}
                </TouchableOpacity>
                <View style={styles.userInfo}>
                  <View style={styles.userNameContainer}>
                    <Text style={styles.userName}>
                      Hi, {userData.hidePersonalInfo ? '••••••' : userData.firstName}! <Text style={styles.eyeIcon}>{userData.hidePersonalInfo ? '👁️' : '👁️‍🗨️'}</Text>
                    </Text>
                    <TouchableOpacity
                      style={styles.eyeButton}
                      onPress={toggleHidePersonalInfo}
                    >
                    </TouchableOpacity>
                  </View>
                  <Text style={styles.userControlNumber}>ID: {userData.controlNumber}</Text>
                </View>
              </View>
              <TouchableOpacity
                style={styles.calendarButton}
                onPress={() => router.push('/mood-tracker')}
              >
                <Text style={styles.calendarDateIcon}>17</Text>
              </TouchableOpacity>
            </View>

            {/* Inspirational Quote */}
            <Animated.View
              style={[
                styles.quoteContainer,
                { opacity: quoteOpacity }
              ]}
            >
              <Text style={styles.quoteText}>"{quote}"</Text>
            </Animated.View>
          </View>
        </View>

        <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
          {/* Mood Tracker Card with Animation */}
          <Animated.View
            style={[
              styles.moodTrackerCard,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <View style={styles.cardGradient}>
              <Text style={styles.cardTitle}>How are you feeling today?</Text>
              <View style={styles.moodOptions}>
                {moodOptions.map((mood, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.moodOption,
                      selectedMood === index && styles.selectedMoodOption
                    ]}
                    onPress={() => handleMoodSelect(mood, index)}
                  >
                    <View style={[
                      styles.moodEmojiContainer,
                      selectedMood === index && styles.selectedMoodEmojiContainer
                    ]}>
                      <Text style={styles.moodEmoji}>{mood}</Text>
                    </View>
                    <Text style={[
                      styles.moodLabel,
                      selectedMood === index && styles.selectedMoodLabel
                    ]}>
                      {moodLabels[index]}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </Animated.View>

          {/* AI Chatbot Card */}
          <Animated.View
            style={[
              styles.supportCard,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <View style={styles.pinkCardBackground}>
              <View style={styles.cardHeader}>
                <View style={styles.cardIconContainer}>
                  <Text style={styles.cardIcon}>🤖</Text>
                </View>
                <Text style={styles.cardHeaderTitle}>AI Mental Health Assistant</Text>
              </View>
              <Text style={styles.supportTitle}>Feeling overwhelmed or need someone to talk to?</Text>
              <Text style={styles.supportDescription}>
                Our AI assistant is here to listen, provide support, and offer guidance 24/7.
              </Text>
              <TouchableOpacity
                style={styles.chatButton}
                onPress={() => router.push('/ai-chatbot')}
              >
                <Text style={styles.chatButtonIcon}>💬</Text>
                <Text style={styles.chatButtonText}>Chat with AIRA</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Stress Management Card */}
          <Animated.View
            style={[
              styles.stressCard,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#E8F3D8', '#D8EAC0']}
              style={styles.cardGradient}
            >
              <View style={styles.cardHeader}>
                <View style={styles.cardIconContainer}>
                  <Text style={styles.cardIcon}>🧘</Text>
                </View>
                <Text style={styles.cardHeaderTitle}>Stress Management</Text>
              </View>
              <Text style={styles.supportDescription}>
                Track your thoughts, feelings, and activities to identify patterns and improve your mental well-being.
              </Text>
              <TouchableOpacity
                style={styles.startButton}
                onPress={() => router.push('/mood-journal')}
              >
                <Text style={styles.startButtonIcon}>📝</Text>
                <Text style={styles.startButtonText}>Start Journaling</Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>

          {/* Assessment Card */}
          <Animated.View
            style={[
              styles.assessmentCard,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#E0F0FF', '#C8E4FF']}
              style={styles.cardGradient}
            >
              <View style={styles.cardHeader}>
                <View style={styles.cardIconContainer}>
                  <Text style={styles.cardIcon}>🧠</Text>
                </View>
                <Text style={styles.cardHeaderTitle}>Mental Health Assessment</Text>
              </View>
              <Text style={styles.supportDescription}>
                Take a quick assessment to better understand your current mental health state and get personalized recommendations.
              </Text>
              <TouchableOpacity
                style={styles.assessmentButton}
                onPress={() => router.push('/mental-assessment')}
              >
                <Text style={styles.assessmentButtonIcon}>📋</Text>
                <Text style={styles.assessmentButtonText}>Take Assessment</Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>
        </ScrollView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  safeArea: {
    flex: 1,
    paddingTop: Platform.OS === 'ios' ? 0 : StatusBar.currentHeight,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 15,
    paddingBottom: 25,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    width: '100%',
    height: 180,
    position: 'relative',
  },
  headerContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 15,
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    flexShrink: 1,
  },
  profilePicture: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#6B9142',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  profileInitial: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  userInfo: {
    justifyContent: 'center',
    flex: 1,
    flexShrink: 1,
  },
  userNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginRight: 5,
    flexShrink: 1,
  },
  eyeIcon: {
    fontSize: 14,
  },
  eyeButton: {
    padding: 5,
  },
  userControlNumber: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  calendarButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
    marginLeft: 8,
  },
  calendarDateIcon: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  quoteContainer: {
    marginTop: 15,
    marginHorizontal: 20,
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    borderRadius: 15,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    width: '90%',
    alignSelf: 'center',
  },
  quoteText: {
    color: '#FFFFFF',
    fontStyle: 'italic',
    textAlign: 'center',
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500',
    flexWrap: 'wrap',
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 100,
  },
  moodTrackerCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  cardGradient: {
    padding: 20,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
    textAlign: 'center',
  },
  moodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  moodOption: {
    alignItems: 'center',
    marginHorizontal: 5,
  },
  moodEmojiContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  selectedMoodEmojiContainer: {
    backgroundColor: '#6B9142',
    shadowColor: '#6B9142',
    shadowOpacity: 0.3,
    elevation: 4,
  },
  moodEmoji: {
    fontSize: 24,
  },
  moodLabel: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  selectedMoodLabel: {
    color: '#6B9142',
    fontWeight: 'bold',
  },
  supportCard: {
    backgroundColor: '#FFE8E8',
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  supportCardHelp: {
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#FF9800',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  helpButton: {
    backgroundColor: '#FF9800',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#FF9800',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  helpButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  helpButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  paymentCard: {
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#00BCD4',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  paymentButton: {
    backgroundColor: '#00BCD4',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#00BCD4',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  paymentButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  inquiriesCard: {
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  inquiryButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  inquiryButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  inquiryButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  pinkCardBackground: {
    backgroundColor: '#FFE8E8',
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  cardIcon: {
    fontSize: 20,
  },
  cardHeaderTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  supportTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  supportDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 15,
  },
  chatButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  chatButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  stressCard: {
    backgroundColor: '#E8F3D8',
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  startButton: {
    backgroundColor: '#8FB565',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#8FB565',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  startButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  assessmentCard: {
    backgroundColor: '#E0F0FF',
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  assessmentButton: {
    backgroundColor: '#4A90E2',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#4A90E2',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  assessmentButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  assessmentButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  appointmentCard: {
    backgroundColor: '#F5E6FF',
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  consultButton: {
    backgroundColor: '#9C6ADE',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#9C6ADE',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  consultButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  consultButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  spacer: {
    height: 25,
  },
});

export default Dashboard;
