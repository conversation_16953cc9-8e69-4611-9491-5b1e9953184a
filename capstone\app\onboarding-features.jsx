import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View, SafeAreaView, Image, TouchableOpacity, Animated, Easing } from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ONBOARDING_KEY = 'hasOnboarded';

const OnboardingFeatures = () => {
  const router = useRouter();
  const [contentReady, setContentReady] = useState(false);

  const fadeAnim = useState(new Animated.Value(0))[0];
  const translateYAnim = useState(new Animated.Value(50))[0];
  const iconScaleAnim = useState(new Animated.Value(0))[0];

  useEffect(() => {
    setContentReady(true);
  }, []);

  useEffect(() => {
    if (contentReady) {
      Animated.sequence([
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 800,
            easing: Easing.ease,
            useNativeDriver: true,
          }),
          Animated.timing(translateYAnim, {
            toValue: 0,
            duration: 800,
            easing: Easing.ease,
            useNativeDriver: true,
          }),
        ]),
        Animated.stagger(100, [
          Animated.spring(iconScaleAnim, { toValue: 1, friction: 5, useNativeDriver: true }),
          Animated.spring(iconScaleAnim, { toValue: 1, friction: 5, useNativeDriver: true }),
          Animated.spring(iconScaleAnim, { toValue: 1, friction: 5, useNativeDriver: true }),
          Animated.spring(iconScaleAnim, { toValue: 1, friction: 5, useNativeDriver: true }),
          Animated.spring(iconScaleAnim, { toValue: 1, friction: 5, useNativeDriver: true }),
        ]),
      ]).start();
    }
  }, [contentReady]);

  const handleNext = async () => {
    try {
      await AsyncStorage.setItem(ONBOARDING_KEY, 'true');
      router.replace('/index'); // Navigate to the main welcome screen
    } catch (e) {
      console.error("Failed to save onboarding status to AsyncStorage", e);
    }
  };

  if (!contentReady) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <Animated.View style={[styles.logoContainer, { opacity: fadeAnim, transform: [{ translateY: translateYAnim }] }]}>
        <Image
          source={require('../../assets/splash-icon.png')}
          style={styles.logo}
          resizeMode="contain"
        />
        <Text style={styles.logoText}>mentalease</Text>
      </Animated.View>

      <Animated.View style={[styles.contentContainer, { opacity: fadeAnim, transform: [{ translateY: translateYAnim }] }]}>
        <Text style={styles.featuresTitle}>App features</Text>
        <View style={styles.featuresGrid}>
          <Animated.View style={[styles.featureItem, { transform: [{ scale: iconScaleAnim }] }]}>
            <Image source={require('../../assets/mood-tracker-icon.png')} style={styles.featureIcon} />
            <Text style={styles.featureText}>Mood tracker</Text>
          </Animated.View>
          <Animated.View style={[styles.featureItem, { transform: [{ scale: iconScaleAnim }] }]}>
            <Image source={require('../../assets/ai-chatbot-icon.png')} style={styles.featureIcon} />
            <Text style={styles.featureText}>AI chatbot</Text>
          </Animated.View>
          <Animated.View style={[styles.featureItem, { transform: [{ scale: iconScaleAnim }] }]}>
            <Image source={require('../../assets/stress-management-icon.png')} style={styles.featureIcon} />
            <Text style={styles.featureText}>Stress Management</Text>
          </Animated.View>
          <Animated.View style={[styles.featureItem, { transform: [{ scale: iconScaleAnim }] }]}>
            <Image source={require('../../assets/mental-health-assessment-icon.png')} style={styles.featureIcon} />
            <Text style={styles.featureText}>Mental Health Assessment</Text>
          </Animated.View>
          <Animated.View style={[styles.featureItem, { transform: [{ scale: iconScaleAnim }] }]}>
            <Image source={require('../../assets/appointment-icon.png')} style={styles.featureIcon} />
            <Text style={styles.featureText}>Appointment</Text>
          </Animated.View>
        </View>
      </Animated.View>

      <Animated.Text style={[styles.partnershipText, { opacity: fadeAnim }]}>
        in Partnership with Sanda Diagnostic Center
      </Animated.Text>

      <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
        <Text style={styles.nextButtonText}>Next</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default OnboardingFeatures;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F0ED',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  logo: {
    width: 100,
    height: 100,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3A6B5B',
    marginTop: 5,
  },
  contentContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  featuresTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#3A6B5B',
    marginBottom: 30,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'flex-start',
    width: '100%',
  },
  featureItem: {
    alignItems: 'center',
    width: '33%', // 3 items per row
    marginBottom: 30,
  },
  featureIcon: {
    width: 60,
    height: 60,
    marginBottom: 10,
  },
  featureText: {
    fontSize: 14,
    color: '#3A6B5B',
    textAlign: 'center',
  },
  partnershipText: {
    fontSize: 14,
    color: '#3A6B5B',
    marginBottom: 10,
  },
  nextButton: {
    backgroundColor: '#3A6B5B',
    borderRadius: 30,
    paddingVertical: 15,
    paddingHorizontal: 50,
    alignItems: 'center',
    marginTop: 20,
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});