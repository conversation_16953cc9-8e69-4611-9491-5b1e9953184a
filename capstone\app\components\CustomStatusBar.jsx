import React from 'react';
import { StatusBar, View, StyleSheet, Platform } from 'react-native';
import Constants from 'expo-constants';

const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight;

const CustomStatusBar = ({ backgroundColor = 'transparent', barStyle = "dark-content", ...props }) => {
  return (
    <View style={[styles.statusBar, { backgroundColor }]}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle={barStyle}
        {...props}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  statusBar: {
    height: STATUSBAR_HEIGHT,
    width: '100%',
  },
});

export default CustomStatusBar;
