import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Modal,
  ActivityIndicator,
  BackHandler
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useUser } from './context/UserContext';

const PersonalInformation = () => {
  const router = useRouter();
  const { userData, updateUserData } = useUser();
  const [isLoading, setIsLoading] = useState(true);

  // Handle back button press
  useEffect(() => {
    // Custom back button handler
    const handleBackPress = () => {
      // Navigate to email verification screen
      router.push('/email-verification');
      return true; // Prevent default behavior
    };

    // Add event listener for hardware back button (Android)
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    }

    // Cleanup function
    return () => {
      if (Platform.OS === 'android') {
        BackHandler.removeEventListener('hardwareBackPress', handleBackPress);
      }
    };
  }, [router]);

  // Check if user already has personal information
  useEffect(() => {
    console.log("Personal information check - User data:", JSON.stringify(userData));

    // First check: if user has explicitly completed profile flag
    if (userData.hasCompletedProfile) {
      console.log("User has completed profile flag, redirecting to dashboard");
      router.replace('/dashboard');
      return;
    }

    // Second check: if user is verified and has firstName, they already have personal info
    if (userData.emailVerified && userData.firstName) {
      console.log("User already has personal information, redirecting to dashboard");
      router.replace('/dashboard');
      return;
    }

    // Third check: if user is verified but doesn't have personal info yet, continue with form
    // This is for new users who have verified their email
    if (userData.emailVerified && !userData.firstName) {
      console.log("User needs to complete personal information");
      setIsLoading(false);
      return;
    }

    // Fourth check: if user is not verified, redirect to email verification
    if (!userData.emailVerified) {
      console.log("User is not verified, redirecting to email verification");
      router.replace('/email-verification');
      return;
    }

    setIsLoading(false);
  }, [userData, router]);

  // Options for dropdowns
  const genderOptions = ['Male', 'Female', 'Non-binary', 'Prefer not to say', 'Other'];
  const civilStatusOptions = ['Single', 'Married', 'Divorced', 'Widowed', 'Separated', 'Other'];

  const [formData, setFormData] = useState({
    firstName: '',
    middleName: '',
    lastName: '',
    age: '',
    gender: '',
    civilStatus: '',
    phone: '',
    birthdate: '',
    birthplace: '',
    religion: '',
    address: ''
  });

  const [errors, setErrors] = useState({});
  const [showGenderModal, setShowGenderModal] = useState(false);
  const [showCivilStatusModal, setShowCivilStatusModal] = useState(false);

  const validateForm = () => {
    let isValid = true;
    const newErrors = {};

    // Required fields validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
      isValid = false;
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
      isValid = false;
    }

    if (!formData.age.trim()) {
      newErrors.age = 'Age is required';
      isValid = false;
    } else if (isNaN(formData.age) || parseInt(formData.age) <= 0) {
      newErrors.age = 'Please enter a valid age';
      isValid = false;
    }

    if (!formData.gender.trim()) {
      newErrors.gender = 'Gender is required';
      isValid = false;
    }

    if (!formData.civilStatus.trim()) {
      newErrors.civilStatus = 'Civil status is required';
      isValid = false;
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
      isValid = false;
    }

    if (!formData.birthdate.trim()) {
      newErrors.birthdate = 'Birthdate is required';
      isValid = false;
    }

    if (!formData.birthplace.trim()) {
      newErrors.birthplace = 'Birthplace is required';
      isValid = false;
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      // Update user data in context with callback to ensure navigation happens after context update
      updateUserData({
        firstName: formData.firstName.trim(),
        middleName: formData.middleName.trim(),
        lastName: formData.lastName.trim(),
        age: parseInt(formData.age),
        gender: formData.gender.trim(),
        civilStatus: formData.civilStatus.trim(),
        phone: formData.phone.trim(),
        birthdate: formData.birthdate.trim(),
        birthplace: formData.birthplace.trim(),
        religion: formData.religion.trim() || 'Not specified',
        address: formData.address.trim(),
        emailVerified: true,
        hasCompletedProfile: true // Mark profile as completed
      }, () => {
        // Navigate to dashboard - use replace to prevent going back
        router.replace('/dashboard');
      });
    } else {
      Alert.alert(
        "Incomplete Information",
        "Please fill in all required fields correctly.",
        [{ text: "OK" }]
      );
    }
  };

  // Show loading indicator while checking user data
  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#6B9142" />
        <Text style={styles.loadingText}>Checking account information...</Text>
      </SafeAreaView>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.push('/email-verification')}
          >
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Personal Information</Text>
        </View>

        <ScrollView style={styles.scrollView}>
          <View style={styles.contentContainer}>
            <Text style={styles.title}>Complete Your Profile</Text>
            <Text style={styles.subtitle}>
              Please provide your personal information to continue.
              Fields marked with * are required.
            </Text>

            <View style={styles.formSection}>
              <Text style={styles.sectionTitle}>Basic Information</Text>

              <Text style={styles.inputLabel}>First Name *</Text>
              <TextInput
                style={[styles.input, errors.firstName && styles.inputError]}
                placeholder="Enter your first name"
                value={formData.firstName}
                onChangeText={(text) => setFormData({...formData, firstName: text})}
              />
              {errors.firstName && <Text style={styles.errorText}>{errors.firstName}</Text>}

              <Text style={styles.inputLabel}>Middle Name</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your middle name (optional)"
                value={formData.middleName}
                onChangeText={(text) => setFormData({...formData, middleName: text})}
              />

              <Text style={styles.inputLabel}>Last Name *</Text>
              <TextInput
                style={[styles.input, errors.lastName && styles.inputError]}
                placeholder="Enter your last name"
                value={formData.lastName}
                onChangeText={(text) => setFormData({...formData, lastName: text})}
              />
              {errors.lastName && <Text style={styles.errorText}>{errors.lastName}</Text>}

              <Text style={styles.inputLabel}>Age *</Text>
              <TextInput
                style={[styles.input, errors.age && styles.inputError]}
                placeholder="Enter your age"
                value={formData.age}
                onChangeText={(text) => setFormData({...formData, age: text})}
                keyboardType="numeric"
              />
              {errors.age && <Text style={styles.errorText}>{errors.age}</Text>}

              <Text style={styles.inputLabel}>Gender *</Text>
              <TouchableOpacity
                style={[styles.input, styles.dropdownInput, errors.gender && styles.inputError]}
                onPress={() => setShowGenderModal(true)}
              >
                <Text style={formData.gender ? styles.dropdownText : styles.placeholderText}>
                  {formData.gender || "Select your gender"}
                </Text>
                <Text style={styles.dropdownIcon}>▼</Text>
              </TouchableOpacity>
              {errors.gender && <Text style={styles.errorText}>{errors.gender}</Text>}

              <Text style={styles.inputLabel}>Civil Status *</Text>
              <TouchableOpacity
                style={[styles.input, styles.dropdownInput, errors.civilStatus && styles.inputError]}
                onPress={() => setShowCivilStatusModal(true)}
              >
                <Text style={formData.civilStatus ? styles.dropdownText : styles.placeholderText}>
                  {formData.civilStatus || "Select your civil status"}
                </Text>
                <Text style={styles.dropdownIcon}>▼</Text>
              </TouchableOpacity>
              {errors.civilStatus && <Text style={styles.errorText}>{errors.civilStatus}</Text>}
            </View>

            <View style={styles.formSection}>
              <Text style={styles.sectionTitle}>Contact Information</Text>

              <Text style={styles.inputLabel}>Phone Number *</Text>
              <TextInput
                style={[styles.input, errors.phone && styles.inputError]}
                placeholder="Enter your phone number"
                value={formData.phone}
                onChangeText={(text) => setFormData({...formData, phone: text})}
                keyboardType="phone-pad"
              />
              {errors.phone && <Text style={styles.errorText}>{errors.phone}</Text>}

              <Text style={styles.inputLabel}>Address *</Text>
              <TextInput
                style={[styles.input, styles.multilineInput, errors.address && styles.inputError]}
                placeholder="Enter your complete address"
                value={formData.address}
                onChangeText={(text) => setFormData({...formData, address: text})}
                multiline
                numberOfLines={3}
              />
              {errors.address && <Text style={styles.errorText}>{errors.address}</Text>}
            </View>

            <View style={styles.formSection}>
              <Text style={styles.sectionTitle}>Background Information</Text>

              <Text style={styles.inputLabel}>Birthdate *</Text>
              <TextInput
                style={[styles.input, errors.birthdate && styles.inputError]}
                placeholder="YYYY-MM-DD"
                value={formData.birthdate}
                onChangeText={(text) => setFormData({...formData, birthdate: text})}
              />
              {errors.birthdate && <Text style={styles.errorText}>{errors.birthdate}</Text>}

              <Text style={styles.inputLabel}>Birthplace *</Text>
              <TextInput
                style={[styles.input, errors.birthplace && styles.inputError]}
                placeholder="Enter your birthplace"
                value={formData.birthplace}
                onChangeText={(text) => setFormData({...formData, birthplace: text})}
              />
              {errors.birthplace && <Text style={styles.errorText}>{errors.birthplace}</Text>}

              <Text style={styles.inputLabel}>Religion</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your religion (optional)"
                value={formData.religion}
                onChangeText={(text) => setFormData({...formData, religion: text})}
              />
            </View>

            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleSubmit}
            >
              <Text style={styles.submitButtonText}>Submit and Continue</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Gender Selection Modal */}
        <Modal
          visible={showGenderModal}
          transparent={true}
          animationType="slide"
        >
          <TouchableWithoutFeedback onPress={() => setShowGenderModal(false)}>
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Select Gender</Text>

                <ScrollView style={styles.optionsList}>
                  {genderOptions.map((option, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.optionItem,
                        formData.gender === option && styles.selectedOption
                      ]}
                      onPress={() => {
                        setFormData({...formData, gender: option});
                        setShowGenderModal(false);
                      }}
                    >
                      <Text style={[
                        styles.optionText,
                        formData.gender === option && styles.selectedOptionText
                      ]}>
                        {option}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>

                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setShowGenderModal(false)}
                >
                  <Text style={styles.closeButtonText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>

        {/* Civil Status Selection Modal */}
        <Modal
          visible={showCivilStatusModal}
          transparent={true}
          animationType="slide"
        >
          <TouchableWithoutFeedback onPress={() => setShowCivilStatusModal(false)}>
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Select Civil Status</Text>

                <ScrollView style={styles.optionsList}>
                  {civilStatusOptions.map((option, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.optionItem,
                        formData.civilStatus === option && styles.selectedOption
                      ]}
                      onPress={() => {
                        setFormData({...formData, civilStatus: option});
                        setShowCivilStatusModal(false);
                      }}
                    >
                      <Text style={[
                        styles.optionText,
                        formData.civilStatus === option && styles.selectedOptionText
                      ]}>
                        {option}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>

                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setShowCivilStatusModal(false)}
                >
                  <Text style={styles.closeButtonText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default PersonalInformation;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: '#666666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 5,
    marginRight: 10,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 20,
  },
  formSection: {
    marginBottom: 20,
    backgroundColor: '#F5F9EE',
    borderRadius: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    paddingBottom: 5,
  },
  inputLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 5,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginBottom: 15,
    fontSize: 14,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  multilineInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: '#FF6B6B',
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 12,
    marginTop: -10,
    marginBottom: 10,
  },
  submitButton: {
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    width: '100%',
    marginTop: 10,
    marginBottom: 30,
    shadowColor: '#6B9142',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 4,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  dropdownInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    color: '#333333',
    fontSize: 14,
  },
  placeholderText: {
    color: '#999999',
    fontSize: 14,
  },
  dropdownIcon: {
    fontSize: 12,
    color: '#6B9142',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 15,
    textAlign: 'center',
  },
  optionsList: {
    maxHeight: 300,
  },
  optionItem: {
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  selectedOption: {
    backgroundColor: '#F5F9EE',
  },
  optionText: {
    fontSize: 16,
    color: '#333333',
  },
  selectedOptionText: {
    color: '#6B9142',
    fontWeight: 'bold',
  },
  closeButton: {
    marginTop: 15,
    paddingVertical: 10,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  closeButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
});
