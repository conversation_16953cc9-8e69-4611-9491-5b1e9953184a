import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, SafeAreaView, Image, Animated, Easing } from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ONBOARDING_KEY = 'hasOnboarded';

const Welcome = () => {
  const router = useRouter();
  const [contentReady, setContentReady] = useState(false);

  const logoFadeAnim = useState(new Animated.Value(0))[0];
  const textFadeAnim = useState(new Animated.Value(0))[0];
  const buttonFadeAnim = useState(new Animated.Value(0))[0];
  const logoScaleAnim = useState(new Animated.Value(0.8))[0];
  const textTranslateYAnim = useState(new Animated.Value(50))[0];
  const buttonTranslateYAnim = useState(new Animated.Value(50))[0];

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        const hasOnboarded = await AsyncStorage.getItem(ONBOARDING_KEY);
        if (hasOnboarded === 'true') {
          // If already onboarded, navigate directly to dashboard or main app screen
          // For now, we'll let it show the welcome screen for demonstration
          // router.replace('/dashboard');
        }
      } catch (e) {
        console.error("Failed to read onboarding status from AsyncStorage", e);
      } finally {
        setContentReady(true);
      }
    };

    checkOnboardingStatus();
  }, []);

  useEffect(() => {
    if (contentReady) {
      Animated.sequence([
        Animated.parallel([
          Animated.timing(logoFadeAnim, {
            toValue: 1,
            duration: 800,
            easing: Easing.ease,
            useNativeDriver: true,
          }),
          Animated.spring(logoScaleAnim, {
            toValue: 1,
            friction: 5,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(textFadeAnim, {
            toValue: 1,
            duration: 800,
            easing: Easing.ease,
            useNativeDriver: true,
          }),
          Animated.timing(textTranslateYAnim, {
            toValue: 0,
            duration: 800,
            easing: Easing.ease,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(buttonFadeAnim, {
            toValue: 1,
            duration: 800,
            easing: Easing.ease,
            useNativeDriver: true,
          }),
          Animated.timing(buttonTranslateYAnim, {
            toValue: 0,
            duration: 800,
            easing: Easing.ease,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    }
  }, [contentReady]);

  const handleGetStarted = async () => {
    try {
      await AsyncStorage.setItem(ONBOARDING_KEY, 'true');
      router.push('/consent');
    } catch (e) {
      console.error("Failed to save onboarding status to AsyncStorage", e);
    }
  };

  if (!contentReady) {
    return null; // Or a loading indicator
  }

  return (
    <SafeAreaView style={styles.container}>
      <Animated.View style={[styles.logoContainer, { opacity: logoFadeAnim, transform: [{ scale: logoScaleAnim }] }]}>
        <Image
          source={require('../../assets/splash-icon.png')}
          style={styles.logo}
          resizeMode="contain"
        />
        <Text style={styles.logoText}>mentalease</Text>
      </Animated.View>

      <Animated.View style={[styles.contentContainer, { opacity: textFadeAnim, transform: [{ translateY: textTranslateYAnim }] }]}>
        <Text style={styles.welcomeTitle}>Welcome to</Text>
        <Text style={styles.mentaleaseTitle}>mentalease</Text>
        <Text style={styles.description}>
          Your mobile companion for emotional well-being. Meet Aira, your mental chatbot companion,
          and access easy appointments and assessments for your mental well-being journey.
        </Text>
      </Animated.View>

      <Animated.View style={[styles.buttonContainer, { opacity: buttonFadeAnim, transform: [{ translateY: buttonTranslateYAnim }] }]}>
        <TouchableOpacity
          style={styles.accountButton}
          onPress={() => router.push('/sign-in')}
        >
          <Text style={styles.accountButtonText}>I already have an account</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.startedButton}
          onPress={handleGetStarted}
        >
          <Text style={styles.startedButtonText}>Let's get started</Text>
        </TouchableOpacity>
      </Animated.View>

      <Animated.Text style={[styles.partnershipText, { opacity: buttonFadeAnim }]}>
        in Partnership with Sanda Diagnostic Center
      </Animated.Text>
    </SafeAreaView>
  );
};

export default Welcome;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F0ED', // Light green background from the design
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  logo: {
    width: 120,
    height: 120,
  },
  logoText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#3A6B5B',
    marginTop: 5,
  },
  contentContainer: {
    alignItems: 'center',
    paddingHorizontal: 30,
    flex: 1,
    justifyContent: 'center',
  },
  welcomeTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#3A6B5B',
    textAlign: 'center',
  },
  mentaleaseTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#3A6B5B',
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: 30,
    marginBottom: 20,
  },
  accountButton: {
    backgroundColor: '#3A6B5B', // Dark green button
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 15,
  },
  accountButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  startedButton: {
    backgroundColor: '#A0C4B5', // Lighter green button
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
  },
  startedButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  partnershipText: {
    fontSize: 14,
    color: '#3A6B5B',
    marginBottom: 20,
  },
});