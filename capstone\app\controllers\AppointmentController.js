// AppointmentController.js - Controller for appointment functionality
import { useAppointmentModel, therapists, formatDateString } from '../models/AppointmentModel';

// Appointment Controller Hook
export const useAppointmentController = () => {
  const {
    appointments,
    upcomingAppointments,
    pastAppointments,
    addAppointment,
    cancelAppointment,
    rescheduleAppointment
  } = useAppointmentModel();

  // Book a new appointment
  const bookAppointment = (therapistId, date, time, notes = '') => {
    // Validate inputs
    if (!therapistId || !date || !time) {
      throw new Error('Missing required appointment information');
    }

    // Format date if it's a Date object
    const formattedDate = date instanceof Date ? formatDateString(date) : date;

    // Create and add the appointment
    const appointment = addAppointment({
      therapistId,
      date: formattedDate,
      time,
      notes
    });

    // In a real app, this would also make an API call or save to storage
    console.log('Appointment booked:', appointment);

    return appointment;
  };

  // Cancel an existing appointment
  const handleCancelAppointment = (appointmentId) => {
    if (!appointmentId) {
      throw new Error('Appointment ID is required');
    }

    const updatedAppointment = cancelAppointment(appointmentId);
    
    // In a real app, this would also make an API call or update storage
    console.log('Appointment cancelled:', updatedAppointment);

    return updatedAppointment;
  };

  // Reschedule an existing appointment
  const handleRescheduleAppointment = (appointmentId, newDate, newTime) => {
    if (!appointmentId || !newDate || !newTime) {
      throw new Error('Missing required rescheduling information');
    }

    // Format date if it's a Date object
    const formattedDate = newDate instanceof Date ? formatDateString(newDate) : newDate;

    const updatedAppointment = rescheduleAppointment(appointmentId, formattedDate, newTime);
    
    // In a real app, this would also make an API call or update storage
    console.log('Appointment rescheduled:', updatedAppointment);

    return updatedAppointment;
  };

  // Get therapist details by ID
  const getTherapistById = (therapistId) => {
    return therapists.find(therapist => therapist.id === therapistId);
  };

  // Get upcoming appointments
  const getUpcomingAppointments = () => {
    return upcomingAppointments.map(appointment => ({
      ...appointment,
      therapist: getTherapistById(appointment.therapistId)
    }));
  };

  // Get past appointments
  const getPastAppointments = () => {
    return pastAppointments.map(appointment => ({
      ...appointment,
      therapist: getTherapistById(appointment.therapistId)
    }));
  };

  // Check if a specific time slot is available
  const isTimeSlotAvailable = (date, time, excludeAppointmentId = null) => {
    // Format date if it's a Date object
    const formattedDate = date instanceof Date ? formatDateString(date) : date;
    
    // Check if there's any existing appointment at this time (excluding the one being rescheduled)
    const existingAppointment = appointments.find(
      appointment => 
        appointment.date === formattedDate && 
        appointment.time === time && 
        appointment.status === 'scheduled' &&
        appointment.id !== excludeAppointmentId
    );
    
    return !existingAppointment;
  };

  return {
    bookAppointment,
    cancelAppointment: handleCancelAppointment,
    rescheduleAppointment: handleRescheduleAppointment,
    getTherapistById,
    getUpcomingAppointments,
    getPastAppointments,
    isTimeSlotAvailable,
    therapists
  };
};

export default useAppointmentController;
