import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  TextInput,
  Modal,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { useState } from 'react';
import { useRouter } from 'expo-router';

const OnlineConsultation = () => {
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [selectedTherapist, setSelectedTherapist] = useState(null);
  const [notes, setNotes] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // Mock data for therapists
  const therapists = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      specialty: 'Anxiety & Depression',
      experience: '10 years',
      rating: 4.9,
      image: '👩‍⚕️',
      available: true
    },
    {
      id: 2,
      name: 'Dr. <PERSON>',
      specialty: 'Stress Management',
      experience: '8 years',
      rating: 4.7,
      image: '👨‍⚕️',
      available: true
    },
    {
      id: 3,
      name: 'Dr. <PERSON> <PERSON>',
      specialty: 'Trauma & PTSD',
      experience: '12 years',
      rating: 4.8,
      image: '👩‍⚕️',
      available: false
    }
  ];

  // Generate dates for the next 7 days
  const getDates = () => {
    const dates = [];
    const today = new Date();

    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }

    return dates;
  };

  // Generate time slots
  const getTimeSlots = () => {
    return [
      '9:00 AM', '10:00 AM', '11:00 AM',
      '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM'
    ];
  };

  const formatDate = (date) => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    return {
      day: days[date.getDay()],
      date: date.getDate(),
      month: months[date.getMonth()]
    };
  };

  const handleBookAppointment = () => {
    if (selectedDate && selectedTime && selectedTherapist) {
      setShowConfirmModal(true);
    }
  };

  const confirmAppointment = () => {
    setShowConfirmModal(false);
    setShowSuccessModal(true);
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    router.push('/dashboard');
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Online Consultation</Text>
          </View>

          <ScrollView contentContainerStyle={styles.content}>
            <Text style={styles.sectionTitle}>Select a Therapist</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.therapistsContainer}
            >
              {therapists.map((therapist) => (
                <TouchableOpacity
                  key={therapist.id}
                  style={[
                    styles.therapistCard,
                    selectedTherapist?.id === therapist.id && styles.selectedTherapistCard,
                    !therapist.available && styles.unavailableTherapistCard
                  ]}
                  onPress={() => therapist.available && setSelectedTherapist(therapist)}
                  disabled={!therapist.available}
                >
                  <Text style={styles.therapistImage}>{therapist.image}</Text>
                  <Text style={styles.therapistName}>{therapist.name}</Text>
                  <Text style={styles.therapistSpecialty}>{therapist.specialty}</Text>
                  <View style={styles.therapistInfoRow}>
                    <Text style={styles.therapistExperience}>{therapist.experience}</Text>
                    <Text style={styles.therapistRating}>⭐ {therapist.rating}</Text>
                  </View>
                  {!therapist.available && (
                    <View style={styles.unavailableOverlay}>
                      <Text style={styles.unavailableText}>Unavailable</Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a Date</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.datesContainer}
            >
              {getDates().map((date, index) => {
                const formattedDate = formatDate(date);
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.dateCard,
                      selectedDate === date.toDateString() && styles.selectedDateCard
                    ]}
                    onPress={() => setSelectedDate(date.toDateString())}
                  >
                    <Text style={styles.dateDay}>{formattedDate.day}</Text>
                    <Text style={styles.dateNumber}>{formattedDate.date}</Text>
                    <Text style={styles.dateMonth}>{formattedDate.month}</Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a Time</Text>
            <View style={styles.timeContainer}>
              {getTimeSlots().map((time, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.timeCard,
                    selectedTime === time && styles.selectedTimeCard
                  ]}
                  onPress={() => setSelectedTime(time)}
                >
                  <Text
                    style={[
                      styles.timeText,
                      selectedTime === time && styles.selectedTimeText
                    ]}
                  >
                    {time}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.sectionTitle}>Additional Notes</Text>
            <TextInput
              style={styles.notesInput}
              placeholder="Add any notes for your therapist..."
              value={notes}
              onChangeText={setNotes}
              multiline
              textAlignVertical="top"
            />

            <TouchableOpacity
              style={[
                styles.bookButton,
                (!selectedDate || !selectedTime || !selectedTherapist) && styles.disabledButton
              ]}
              onPress={handleBookAppointment}
              disabled={!selectedDate || !selectedTime || !selectedTherapist}
            >
              <Text style={styles.bookButtonText}>Book Appointment</Text>
            </TouchableOpacity>
          </ScrollView>

          {/* Confirmation Modal */}
          <Modal
            visible={showConfirmModal}
            transparent={true}
            animationType="slide"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Confirm Appointment</Text>

                {selectedTherapist && (
                  <View style={styles.confirmTherapist}>
                    <Text style={styles.confirmTherapistImage}>{selectedTherapist.image}</Text>
                    <View style={styles.confirmTherapistInfo}>
                      <Text style={styles.confirmTherapistName}>{selectedTherapist.name}</Text>
                      <Text style={styles.confirmTherapistSpecialty}>{selectedTherapist.specialty}</Text>
                    </View>
                  </View>
                )}

                {selectedDate && selectedTime && (
                  <View style={styles.confirmDateTime}>
                    <Text style={styles.confirmDateTimeText}>
                      {selectedDate}
                    </Text>
                    <Text style={styles.confirmDateTimeText}>at {selectedTime}</Text>
                  </View>
                )}

                <View style={styles.confirmButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowConfirmModal(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={confirmAppointment}
                  >
                    <Text style={styles.confirmButtonText}>Confirm</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Success Modal */}
          <Modal
            visible={showSuccessModal}
            transparent={true}
            animationType="slide"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.successIcon}>✅</Text>
                <Text style={styles.successTitle}>Appointment Booked!</Text>
                <Text style={styles.successMessage}>
                  Your appointment has been successfully scheduled. You will receive a confirmation email shortly.
                </Text>

                <TouchableOpacity
                  style={styles.doneButton}
                  onPress={closeSuccessModal}
                >
                  <Text style={styles.doneButtonText}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default OnlineConsultation;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#6B9142',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 30,
  },
  content: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 15,
    marginTop: 10,
  },
  therapistsContainer: {
    paddingBottom: 10,
  },
  therapistCard: {
    backgroundColor: '#F5F9EE',
    borderRadius: 15,
    padding: 15,
    marginRight: 15,
    width: 160,
    position: 'relative',
  },
  selectedTherapistCard: {
    borderWidth: 2,
    borderColor: '#6B9142',
  },
  unavailableTherapistCard: {
    opacity: 0.7,
  },
  therapistImage: {
    fontSize: 40,
    marginBottom: 10,
    textAlign: 'center',
  },
  therapistName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 5,
  },
  therapistSpecialty: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 10,
  },
  therapistInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  therapistExperience: {
    fontSize: 12,
    color: '#999999',
  },
  therapistRating: {
    fontSize: 12,
    color: '#F5A623',
  },
  unavailableOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unavailableText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 14,
  },
  datesContainer: {
    paddingBottom: 10,
  },
  dateCard: {
    backgroundColor: '#F5F9EE',
    borderRadius: 10,
    padding: 10,
    marginRight: 10,
    alignItems: 'center',
    width: 70,
  },
  selectedDateCard: {
    backgroundColor: '#6B9142',
  },
  dateDay: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 5,
  },
  dateNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 5,
  },
  dateMonth: {
    fontSize: 12,
    color: '#666666',
  },
  timeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  timeCard: {
    backgroundColor: '#F5F9EE',
    borderRadius: 10,
    padding: 10,
    margin: 5,
    width: '30%',
    alignItems: 'center',
  },
  selectedTimeCard: {
    backgroundColor: '#6B9142',
  },
  timeText: {
    fontSize: 14,
    color: '#666666',
  },
  selectedTimeText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  notesInput: {
    backgroundColor: '#F5F9EE',
    borderRadius: 10,
    padding: 15,
    height: 100,
    marginBottom: 20,
    textAlignVertical: 'top',
  },
  bookButton: {
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 20,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  bookButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    width: '85%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 20,
    textAlign: 'center',
  },
  confirmTherapist: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    backgroundColor: '#F5F9EE',
    borderRadius: 10,
    padding: 15,
  },
  confirmTherapistImage: {
    fontSize: 30,
    marginRight: 15,
  },
  confirmTherapistInfo: {
    flex: 1,
  },
  confirmTherapistName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 5,
  },
  confirmTherapistSpecialty: {
    fontSize: 14,
    color: '#666666',
  },
  confirmDateTime: {
    backgroundColor: '#F5F9EE',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  confirmDateTimeText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 5,
  },
  confirmButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#6B9142',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 12,
    alignItems: 'center',
    marginLeft: 10,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  successIcon: {
    fontSize: 50,
    textAlign: 'center',
    marginBottom: 10,
  },
  successTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 20,
  },
  doneButton: {
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 12,
    alignItems: 'center',
  },
  doneButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
