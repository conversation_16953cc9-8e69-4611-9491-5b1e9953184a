{"name": "capstone", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "capstone": "file:", "expo": "~53.0.9", "expo-constants": "~17.1.6", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.4", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}