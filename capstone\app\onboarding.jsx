import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, Image, SafeAreaView, Animated, Easing } from 'react-native';
import { useRouter } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

const Onboarding = () => {
  const router = useRouter();
  const [appIsReady, setAppIsReady] = useState(false);
  const fadeAnim = useState(new Animated.Value(0))[0]; // Initial value for opacity: 0
  const scaleAnim = useState(new Animated.Value(0.8))[0]; // Initial value for scale: 0.8

  useEffect(() => {
    async function prepare() {
      try {
        // Pre-load fonts, make any API calls you need to do here
        // For now, just simulate a delay
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (e) {
        console.warn(e);
      } finally {
        setAppIsReady(true);
        SplashScreen.hideAsync();
        // Start animations after splash screen hides
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 1000,
            easing: Easing.ease,
            useNativeDriver: true,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            friction: 3,
            useNativeDriver: true,
          }),
        ]).start(() => {
          // Navigate to the next screen after animations complete
          // For now, we'll just stay on this screen for demonstration
          // In a real app, you'd navigate to the next onboarding step or main app
          // router.replace('/onboarding-features'); // Example for next step
        });
      }
    }

    prepare();
  }, []);

  if (!appIsReady) {
    return null; // Or a loading indicator if you prefer
  }

  return (
    <SafeAreaView style={styles.container}>
      <Animated.View style={[styles.logoContainer, { opacity: fadeAnim, transform: [{ scale: scaleAnim }] }]}>
        <Image
          source={require('../../assets/splash-icon.png')} // Using splash-icon.png for the logo
          style={styles.logo}
          resizeMode="contain"
        />
        <Text style={styles.logoText}>mentalease</Text>
      </Animated.View>

      <Animated.Text style={[styles.partnershipText, { opacity: fadeAnim }]}>
        in Partnership with Sanda Diagnostic Center
      </Animated.Text>
    </SafeAreaView>
  );
};

export default Onboarding;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F0ED', // Light green background from the design
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 100, // Adjust as needed
  },
  logo: {
    width: 150, // Adjust size as needed
    height: 150, // Adjust size as needed
  },
  logoText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#3A6B5B', // Dark green from the design
    marginTop: 10,
  },
  partnershipText: {
    position: 'absolute',
    bottom: 40,
    fontSize: 14,
    color: '#3A6B5B',
  },
});