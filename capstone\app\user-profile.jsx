import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Switch,
  Alert,
  TextInput,
  Modal,
  Image,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { useUser } from './context/UserContext';

const UserProfile = () => {
  const router = useRouter();
  const { userData, updateUserData, toggleHidePersonalInfo } = useUser();
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showLogoutConfirmation, setShowLogoutConfirmation] = useState(false);
  const [showEditInfoModal, setShowEditInfoModal] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Form data for editing
  const [formData, setFormData] = useState({
    firstName: userData.firstName,
    middleName: userData.middleName,
    lastName: userData.lastName,
    email: userData.email,
    phone: userData.phone,
    age: userData.age,
    gender: userData.gender,
    civilStatus: userData.civilStatus,
    birthdate: userData.birthdate,
    birthplace: userData.birthplace,
    religion: userData.religion,
    address: userData.address
  });

  // Update form data when userData changes
  useEffect(() => {
    setFormData({
      firstName: userData.firstName,
      middleName: userData.middleName,
      lastName: userData.lastName,
      email: userData.email,
      phone: userData.phone,
      age: userData.age,
      gender: userData.gender,
      civilStatus: userData.civilStatus,
      birthdate: userData.birthdate,
      birthplace: userData.birthplace,
      religion: userData.religion,
      address: userData.address
    });
  }, [
    userData.firstName,
    userData.middleName,
    userData.lastName,
    userData.email,
    userData.phone,
    userData.age,
    userData.gender,
    userData.civilStatus,
    userData.birthdate,
    userData.birthplace,
    userData.religion,
    userData.address
  ]);

  const handleToggleChange = (setting) => {
    updateUserData({
      [setting]: !userData[setting]
    });
  };

  const handleChangePassword = () => {
    // Validate passwords
    if (!currentPassword) {
      Alert.alert("Error", "Please enter your current password");
      return;
    }

    if (newPassword.length < 8) {
      Alert.alert("Error", "New password must be at least 8 characters");
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert("Error", "New passwords do not match");
      return;
    }

    // In a real app, you would send this to your API
    Alert.alert("Success", "Password changed successfully");
    setShowPasswordModal(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
  };

  const handleLogout = () => {
    // In a real app, you would clear authentication tokens here
    setShowLogoutConfirmation(false);
    router.replace('/');
  };

  const handlePickImage = async () => {
    try {
      // Request permission to access the photo library
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow access to your photo library to change your profile picture.');
        return;
      }

      // Launch the image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'Images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Update the user data with the selected image
        updateUserData({
          profileImage: { uri: result.assets[0].uri }
        });

        // Show success message
        Alert.alert('Success', 'Profile picture updated successfully');
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'There was an error selecting the image. Please try again.');
    }
  };

  const handleSaveUserInfo = () => {
    // Validate form data
    if (!formData.firstName.trim()) {
      Alert.alert('Error', 'First name cannot be empty');
      return;
    }

    if (!formData.lastName.trim()) {
      Alert.alert('Error', 'Last name cannot be empty');
      return;
    }

    if (!formData.email.trim() || !formData.email.includes('@')) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    if (!formData.phone.trim()) {
      Alert.alert('Error', 'Phone number cannot be empty');
      return;
    }

    // Update user data
    updateUserData({
      firstName: formData.firstName.trim(),
      middleName: formData.middleName.trim(),
      lastName: formData.lastName.trim(),
      email: formData.email.trim(),
      phone: formData.phone.trim(),
      age: formData.age,
      gender: formData.gender,
      civilStatus: formData.civilStatus,
      birthdate: formData.birthdate,
      birthplace: formData.birthplace,
      religion: formData.religion,
      address: formData.address.trim()
    });

    // Close the modal
    setShowEditInfoModal(false);

    // Show success message
    Alert.alert('Success', 'Your information has been updated');
  };

  const securitySettings = [
    {
      id: 'password',
      title: 'Change Password',
      icon: '🔑',
      action: () => setShowPasswordModal(true)
    },
    {
      id: 'twoFactor',
      title: 'Two-Factor Authentication',
      icon: '🔐',
      toggle: true,
      value: userData.twoFactorAuth,
      action: () => handleToggleChange('twoFactorAuth')
    },
    {
      id: 'hideInfo',
      title: 'Hide Personal Information',
      icon: '👁️',
      toggle: true,
      value: userData.hidePersonalInfo,
      action: toggleHidePersonalInfo
    },
    {
      id: 'dataSharing',
      title: 'Data Sharing',
      icon: '📊',
      toggle: true,
      value: userData.dataSharing,
      action: () => handleToggleChange('dataSharing')
    }
  ];

  const appSettings = [
    {
      id: 'notifications',
      title: 'Notifications',
      icon: '🔔',
      toggle: true,
      value: userData.notifications,
      action: () => handleToggleChange('notifications')
    },
    {
      id: 'darkMode',
      title: 'Dark Mode',
      icon: '🌙',
      toggle: true,
      value: userData.darkMode,
      action: () => handleToggleChange('darkMode')
    },
    {
      id: 'language',
      title: 'Language',
      icon: '🌐',
      value: userData.language,
      action: () => Alert.alert("Language", "Language settings would open here")
    }
  ];

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile & Settings</Text>
        </View>

        <ScrollView contentContainerStyle={styles.content}>
          <View style={styles.profileSection}>
            <View style={styles.profileImageContainer}>
              {userData.profileImage ? (
                <Image source={userData.profileImage} style={styles.profileImage} />
              ) : (
                <View style={styles.profileImagePlaceholder}>
                  <Text style={styles.profileInitial}>{userData.firstName.charAt(0)}</Text>
                </View>
              )}
              <TouchableOpacity style={styles.editImageButton} onPress={handlePickImage}>
                <Text style={styles.editImageButtonText}>Edit</Text>
              </TouchableOpacity>
            </View>

            <Text style={styles.userName}>
              {userData.hidePersonalInfo ? '••••••••••' : `${userData.firstName} ${userData.middleName ? userData.middleName + ' ' : ''}${userData.lastName}`}
            </Text>
            <Text style={styles.userEmail}>
              {userData.hidePersonalInfo ? '••••••@•••••.com' : userData.email}
            </Text>
            {userData.emailVerified && (
              <View style={styles.verifiedBadge}>
                <Text style={styles.verifiedText}>✓ Verified</Text>
              </View>
            )}

            <View style={styles.controlNumberContainer}>
              <Text style={styles.controlNumberLabel}>Control Number:</Text>
              <Text style={styles.controlNumberValue}>{userData.controlNumber}</Text>
            </View>
          </View>

          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Personal Information</Text>

            {/* Basic Information */}
            <View style={styles.infoGroup}>
              <Text style={styles.infoGroupTitle}>Basic Information</Text>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>First Name</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '••••••••••' : userData.firstName}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Middle Name</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '••••••••••' : userData.middleName || 'N/A'}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Last Name</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '••••••••••' : userData.lastName}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Age</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '••' : userData.age}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Gender</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '••••••' : userData.gender}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Civil Status</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '••••••' : userData.civilStatus}
                </Text>
              </View>
            </View>

            {/* Contact Information */}
            <View style={styles.infoGroup}>
              <Text style={styles.infoGroupTitle}>Contact Information</Text>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Email</Text>
                <View style={styles.infoValueContainer}>
                  <Text style={styles.infoValue}>
                    {userData.hidePersonalInfo ? '••••••@•••••.com' : userData.email}
                  </Text>
                  {userData.emailVerified && (
                    <View style={styles.smallVerifiedBadge}>
                      <Text style={styles.smallVerifiedText}>✓</Text>
                    </View>
                  )}
                </View>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Phone</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '(•••) •••-••••' : userData.phone}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Address</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '•••••••••••••••••••••••' : userData.address}
                </Text>
              </View>
            </View>

            {/* Background Information */}
            <View style={styles.infoGroup}>
              <Text style={styles.infoGroupTitle}>Background Information</Text>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Birthdate</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '••/••/••••' : userData.birthdate}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Birthplace</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '••••••••••••' : userData.birthplace}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Religion</Text>
                <Text style={styles.infoValue}>
                  {userData.hidePersonalInfo ? '••••••••••' : userData.religion}
                </Text>
              </View>
            </View>

            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setShowEditInfoModal(true)}
            >
              <Text style={styles.editButtonText}>Edit Information</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Security</Text>
            {securitySettings.map((setting) => (
              <View key={setting.id} style={styles.settingItem}>
                <View style={styles.settingIconContainer}>
                  <Text style={styles.settingIcon}>{setting.icon}</Text>
                </View>
                <Text style={styles.settingTitle}>{setting.title}</Text>
                {setting.toggle ? (
                  <Switch
                    value={setting.value}
                    onValueChange={setting.action}
                    trackColor={{ false: '#D1D1D6', true: '#A3C677' }}
                    thumbColor={setting.value ? '#6B9142' : '#F4F4F4'}
                  />
                ) : (
                  <TouchableOpacity onPress={setting.action} style={styles.settingAction}>
                    <Text style={styles.settingActionText}>></Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>

          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>App Settings</Text>
            {appSettings.map((setting) => (
              <View key={setting.id} style={styles.settingItem}>
                <View style={styles.settingIconContainer}>
                  <Text style={styles.settingIcon}>{setting.icon}</Text>
                </View>
                <Text style={styles.settingTitle}>{setting.title}</Text>
                {setting.toggle ? (
                  <Switch
                    value={setting.value}
                    onValueChange={setting.action}
                    trackColor={{ false: '#D1D1D6', true: '#A3C677' }}
                    thumbColor={setting.value ? '#6B9142' : '#F4F4F4'}
                  />
                ) : (
                  <View style={styles.settingValueContainer}>
                    <Text style={styles.settingValue}>{setting.value}</Text>
                    <TouchableOpacity onPress={setting.action} style={styles.settingAction}>
                      <Text style={styles.settingActionText}>></Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            ))}
          </View>

          <TouchableOpacity
            style={styles.logoutButton}
            onPress={() => setShowLogoutConfirmation(true)}
          >
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>

          <View style={styles.versionContainer}>
            <Text style={styles.versionText}>MentalEase v1.0.0</Text>
            <Text style={styles.versionText}>© 2023 MentalEase Inc.</Text>
          </View>
        </ScrollView>

        {/* Password Change Modal */}
        <Modal
          visible={showPasswordModal}
          transparent={true}
          animationType="slide"
        >
          <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Change Password</Text>

                <TextInput
                  style={styles.passwordInput}
                  placeholder="Current Password"
                  secureTextEntry
                  value={currentPassword}
                  onChangeText={setCurrentPassword}
                />

                <TextInput
                  style={styles.passwordInput}
                  placeholder="New Password"
                  secureTextEntry
                  value={newPassword}
                  onChangeText={setNewPassword}
                />

                <TextInput
                  style={styles.passwordInput}
                  placeholder="Confirm New Password"
                  secureTextEntry
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                />

                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => {
                      setShowPasswordModal(false);
                      setCurrentPassword('');
                      setNewPassword('');
                      setConfirmPassword('');
                    }}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.saveButton}
                    onPress={handleChangePassword}
                  >
                    <Text style={styles.saveButtonText}>Save</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>

        {/* Logout Confirmation Modal */}
        <Modal
          visible={showLogoutConfirmation}
          transparent={true}
          animationType="slide"
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Confirm Logout</Text>
              <Text style={styles.modalText}>Are you sure you want to logout?</Text>

              <View style={styles.modalButtons}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowLogoutConfirmation(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.saveButton, styles.logoutConfirmButton]}
                  onPress={handleLogout}
                >
                  <Text style={styles.saveButtonText}>Logout</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Edit Information Modal */}
        <Modal
          visible={showEditInfoModal}
          transparent={true}
          animationType="slide"
        >
          <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Edit Personal Information</Text>

                <ScrollView style={styles.modalScrollView}>
                  {/* Basic Information Section */}
                  <View style={styles.modalSection}>
                    <Text style={styles.modalSectionTitle}>Basic Information</Text>

                    <Text style={styles.inputLabel}>First Name</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.firstName}
                      onChangeText={(text) => setFormData({...formData, firstName: text})}
                      placeholder="Enter your first name"
                    />

                    <Text style={styles.inputLabel}>Middle Name</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.middleName}
                      onChangeText={(text) => setFormData({...formData, middleName: text})}
                      placeholder="Enter your middle name (optional)"
                    />

                    <Text style={styles.inputLabel}>Last Name</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.lastName}
                      onChangeText={(text) => setFormData({...formData, lastName: text})}
                      placeholder="Enter your last name"
                    />

                    <Text style={styles.inputLabel}>Age</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.age.toString()}
                      onChangeText={(text) => setFormData({...formData, age: parseInt(text) || 0})}
                      placeholder="Enter your age"
                      keyboardType="numeric"
                    />

                    <Text style={styles.inputLabel}>Gender</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.gender}
                      onChangeText={(text) => setFormData({...formData, gender: text})}
                      placeholder="Enter your gender"
                    />

                    <Text style={styles.inputLabel}>Civil Status</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.civilStatus}
                      onChangeText={(text) => setFormData({...formData, civilStatus: text})}
                      placeholder="Enter your civil status"
                    />
                  </View>

                  {/* Contact Information Section */}
                  <View style={styles.modalSection}>
                    <Text style={styles.modalSectionTitle}>Contact Information</Text>

                    <Text style={styles.inputLabel}>Email</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.email}
                      onChangeText={(text) => setFormData({...formData, email: text})}
                      placeholder="Enter your email"
                      keyboardType="email-address"
                      autoCapitalize="none"
                    />

                    <Text style={styles.inputLabel}>Phone</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.phone}
                      onChangeText={(text) => setFormData({...formData, phone: text})}
                      placeholder="Enter your phone number"
                      keyboardType="phone-pad"
                    />

                    <Text style={styles.inputLabel}>Address</Text>
                    <TextInput
                      style={[styles.textInput, styles.multilineInput]}
                      value={formData.address}
                      onChangeText={(text) => setFormData({...formData, address: text})}
                      placeholder="Enter your address"
                      multiline
                      numberOfLines={3}
                    />
                  </View>

                  {/* Background Information Section */}
                  <View style={styles.modalSection}>
                    <Text style={styles.modalSectionTitle}>Background Information</Text>

                    <Text style={styles.inputLabel}>Birthdate</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.birthdate}
                      onChangeText={(text) => setFormData({...formData, birthdate: text})}
                      placeholder="YYYY-MM-DD"
                    />

                    <Text style={styles.inputLabel}>Birthplace</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.birthplace}
                      onChangeText={(text) => setFormData({...formData, birthplace: text})}
                      placeholder="Enter your birthplace"
                    />

                    <Text style={styles.inputLabel}>Religion</Text>
                    <TextInput
                      style={styles.textInput}
                      value={formData.religion}
                      onChangeText={(text) => setFormData({...formData, religion: text})}
                      placeholder="Enter your religion"
                    />
                  </View>
                </ScrollView>

                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => {
                      setShowEditInfoModal(false);
                      setFormData({
                        firstName: userData.firstName,
                        middleName: userData.middleName,
                        lastName: userData.lastName,
                        email: userData.email,
                        phone: userData.phone,
                        age: userData.age,
                        gender: userData.gender,
                        civilStatus: userData.civilStatus,
                        birthdate: userData.birthdate,
                        birthplace: userData.birthplace,
                        religion: userData.religion,
                        address: userData.address
                      });
                    }}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.saveButton}
                    onPress={handleSaveUserInfo}
                  >
                    <Text style={styles.saveButtonText}>Save</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default UserProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#6B9142',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 30,
  },
  content: {
    padding: 20,
  },
  profileSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 15,
  },
  profileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#6B9142',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  profileInitial: {
    fontSize: 40,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#6B9142',
  },
  editImageButtonText: {
    color: '#6B9142',
    fontSize: 12,
    fontWeight: '600',
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 5,
  },
  userEmail: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 10,
  },
  controlNumberContainer: {
    flexDirection: 'row',
    backgroundColor: '#F5F9EE',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
  },
  controlNumberLabel: {
    fontSize: 14,
    color: '#666666',
    marginRight: 5,
  },
  controlNumberValue: {
    fontSize: 14,
    color: '#6B9142',
    fontWeight: 'bold',
  },
  sectionContainer: {
    backgroundColor: '#F5F9EE',
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 15,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  infoLabel: {
    fontSize: 14,
    color: '#666666',
  },
  infoValue: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
  editButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingVertical: 10,
    alignItems: 'center',
    marginTop: 15,
    borderWidth: 1,
    borderColor: '#6B9142',
  },
  editButtonText: {
    color: '#6B9142',
    fontSize: 14,
    fontWeight: '600',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  settingIconContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  settingIcon: {
    fontSize: 16,
  },
  settingTitle: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
  },
  settingValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValue: {
    fontSize: 14,
    color: '#666666',
    marginRight: 10,
  },
  settingAction: {
    padding: 5,
  },
  settingActionText: {
    fontSize: 16,
    color: '#6B9142',
    fontWeight: 'bold',
  },
  logoutButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    marginVertical: 20,
  },
  logoutButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  versionContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  versionText: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 5,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    width: '85%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalText: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  passwordInput: {
    backgroundColor: '#F5F9EE',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 15,
    marginBottom: 15,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#6B9142',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 12,
    alignItems: 'center',
    marginLeft: 10,
  },
  logoutConfirmButton: {
    backgroundColor: '#FF6B6B',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  inputLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 5,
    marginTop: 10,
  },
  textInput: {
    backgroundColor: '#F5F9EE',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 15,
    marginBottom: 10,
    fontSize: 14,
    color: '#333333',
  },
  multilineInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalScrollView: {
    maxHeight: 400,
    marginBottom: 15,
  },
  modalSection: {
    marginBottom: 20,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
  },
  infoGroup: {
    marginBottom: 15,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoGroupTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    paddingBottom: 5,
  },
  infoValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  verifiedBadge: {
    backgroundColor: '#6B9142',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 15,
    alignSelf: 'center',
    marginTop: 5,
  },
  verifiedText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  smallVerifiedBadge: {
    backgroundColor: '#6B9142',
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 5,
  },
  smallVerifiedText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
