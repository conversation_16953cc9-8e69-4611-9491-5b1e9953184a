import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  BackHandler
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useUser } from './context/UserContext';

const EmailVerification = () => {
  const router = useRouter();
  const { userData, updateUserData } = useUser();
  const [code, setCode] = useState('');
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Get email from user context
  const email = userData.email || "<EMAIL>";

  // Handle back button press
  useEffect(() => {
    // Custom back button handler
    const handleBackPress = () => {
      // Navigate to create account screen
      router.push('/create-account');
      return true; // Prevent default behavior
    };

    // Add event listener for hardware back button (Android)
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    }

    // Cleanup function
    return () => {
      if (Platform.OS === 'android') {
        BackHandler.removeEventListener('hardwareBackPress', handleBackPress);
      }
    };
  }, [router]);

  // Check if user is already verified
  useEffect(() => {
    console.log("Email verification check - User data:", JSON.stringify(userData));

    // First check: if we have an existing user with emailVerified and hasCompletedProfile
    if (userData.emailVerified && userData.hasCompletedProfile) {
      console.log("User is verified and has completed profile, redirecting to dashboard");
      router.replace('/dashboard');
      return;
    }

    // Second check: if user is verified but needs to complete profile
    if (userData.emailVerified && userData.firstName) {
      console.log("User is verified and has personal info, redirecting to dashboard");
      router.replace('/dashboard');
      return;
    }

    // Third check: if user is verified but has no personal info
    if (userData.emailVerified && !userData.firstName) {
      console.log("User is verified but needs to complete personal info");
      router.replace('/personal-information');
      return;
    }

    // If we get here, user is not verified, show verification screen
    console.log("User is not verified, showing verification screen");
    setIsLoading(false);
  }, [userData, router]);

  useEffect(() => {
    let interval;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer(prevTimer => prevTimer - 1);
      }, 1000);
    } else {
      setCanResend(true);
    }

    return () => clearInterval(interval);
  }, [timer]);

  const handleResendCode = () => {
    if (canResend) {
      // Logic to resend code would go here
      setTimer(60);
      setCanResend(false);
      Alert.alert("Code Resent", "A new verification code has been sent to your email.");
    }
  };

  const handleVerify = () => {
    if (code.length === 6) {
      setIsVerifying(true);

      // Simulate verification process
      setTimeout(() => {
        // Update user context to mark email as verified
        updateUserData({
          email: email,
          emailVerified: true
        });

        // Redirect to personal information form
        router.push('/personal-information');
        setIsVerifying(false);
      }, 1000);
    } else {
      Alert.alert("Invalid Code", "Please enter a valid 6-digit code.");
    }
  };

  // Show loading indicator while checking verification status
  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#6B9142" />
        <Text style={styles.loadingText}>Checking verification status...</Text>
      </SafeAreaView>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.push('/create-account')}
            >
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.logoContainer}>
            {/* Logo placeholder - you'll add the actual image later */}
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>mentalease</Text>
            </View>
          </View>

          <View style={styles.contentContainer}>
            <Text style={styles.title}>Verify Your Email</Text>
            <Text style={styles.subtitle}>
              We've sent a verification code to{'\n'}
              <EMAIL>
            </Text>

            <Text style={styles.inputLabel}>Enter Verification Code</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter 6-digit code"
              value={code}
              onChangeText={setCode}
              keyboardType="number-pad"
              maxLength={6}
              returnKeyType="done"
              onSubmitEditing={Keyboard.dismiss}
            />

            <TouchableOpacity
              style={styles.resendContainer}
              onPress={handleResendCode}
              disabled={!canResend}
            >
              <Text style={styles.resendText}>
                Didn't receive the code?
              </Text>
              <Text style={[styles.resendTimer, canResend && styles.resendActive]}>
                {canResend ? 'Resend code' : `Resend code in ${timer} seconds`}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.verifyButton,
                (code.length !== 6 || isVerifying) && styles.verifyButtonDisabled
              ]}
              onPress={() => {
                Keyboard.dismiss();
                handleVerify();
              }}
              disabled={code.length !== 6 || isVerifying}
            >
              <Text style={styles.verifyButtonText}>
                {isVerifying ? "Verifying..." : "Verify Email"}
              </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default EmailVerification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#6B9142',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  logoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#F5F9EE',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    color: '#6B9142',
    fontSize: 14,
    fontWeight: 'bold',
  },
  contentContainer: {
    backgroundColor: '#F5F9EE',
    borderRadius: 15,
    margin: 20,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 30,
    textAlign: 'center',
    lineHeight: 24,
  },
  inputLabel: {
    fontSize: 14,
    color: '#666666',
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginBottom: 15,
    fontSize: 16,
    width: '100%',
    textAlign: 'center',
    letterSpacing: 2,
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  resendText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 5,
  },
  resendTimer: {
    fontSize: 14,
    color: '#999999',
  },
  resendActive: {
    color: '#6B9142',
    fontWeight: '600',
  },
  verifyButton: {
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    width: '100%',
  },
  verifyButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  verifyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
