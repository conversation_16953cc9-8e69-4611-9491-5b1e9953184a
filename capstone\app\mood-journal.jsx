import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Modal,
  Keyboard,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Platform,
  StatusBar
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import BottomNavigation from './components/BottomNavigation';

const MoodJournal = () => {
  const router = useRouter();
  const { date } = useLocalSearchParams();
  const [selectedMood, setSelectedMood] = useState('');
  const [selectedEmotions, setSelectedEmotions] = useState([]);
  const [journalEntry, setJournalEntry] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [modalContent, setModalContent] = useState({});

  // Format the date for display
  const formatDisplayDate = (dateString) => {
    if (!dateString) return 'Today';

    const [year, month, day] = dateString.split('-');
    const dateObj = new Date(year, month - 1, day);

    return dateObj.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const moods = [
    { emoji: '😊', label: 'Good' },
    { emoji: '😁', label: 'Great' },
    { emoji: '😐', label: 'Okay' },
    { emoji: '😔', label: 'Sad' },
    { emoji: '😫', label: 'Stressed' }
  ];

  const emotions = [
    'Enthusiastic', 'Excited', 'Joyful', 'Loved',
    'Calm', 'Satisfied', 'Grateful', 'Relaxed',
    'Happy', 'Optimistic', 'Sad', 'Disappointed',
    'Anxious', 'Stressed', 'Frustrated', 'Overwhelmed'
  ];

  const handleMoodSelect = (mood) => {
    setSelectedMood(mood);
  };

  const handleEmotionToggle = (emotion) => {
    if (selectedEmotions.includes(emotion)) {
      setSelectedEmotions(selectedEmotions.filter(e => e !== emotion));
    } else {
      setSelectedEmotions([...selectedEmotions, emotion]);
    }
  };

  const handleSave = () => {
    // Here you would save the journal entry to your database
    // For now, we'll just show a modal with recommendations

    let recommendations = [];

    if (selectedMood.label === 'Good' || selectedMood.label === 'Great') {
      recommendations = [
        'Share your positive energy with others',
        'Build on this momentum',
        'Start something you have been wanting to do',
        'Express gratitude to someone',
        'Celebrate your good mood!'
      ];
    } else if (selectedMood.label === 'Okay') {
      recommendations = [
        'Take a short walk outside',
        'Listen to uplifting music',
        'Connect with a friend',
        'Practice mindfulness for 5 minutes',
        'Do something creative'
      ];
    } else {
      recommendations = [
        'Take deep breaths for 2 minutes',
        'Write down what is bothering you',
        'Reach out to someone you trust',
        'Do something kind for yourself',
        'Remember this feeling is temporary'
      ];
    }

    setModalContent({
      mood: selectedMood,
      emotions: selectedEmotions,
      entry: journalEntry,
      recommendations
    });

    setShowModal(true);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>{formatDisplayDate(date)}</Text>
          </View>

          <ScrollView contentContainerStyle={styles.content}>
            <Text style={styles.sectionTitle}>How are you feeling today?</Text>
            <View style={styles.moodContainer}>
              {moods.map((mood) => (
                <TouchableOpacity
                  key={mood.label}
                  style={[
                    styles.moodOption,
                    selectedMood.label === mood.label && styles.selectedMoodOption
                  ]}
                  onPress={() => handleMoodSelect(mood)}
                >
                  <Text style={styles.moodEmoji}>{mood.emoji}</Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.sectionTitle}>What emotions are you feeling?</Text>
            <View style={styles.emotionsContainer}>
              {emotions.map((emotion) => (
                <TouchableOpacity
                  key={emotion}
                  style={[
                    styles.emotionChip,
                    selectedEmotions.includes(emotion) && styles.selectedEmotionChip
                  ]}
                  onPress={() => handleEmotionToggle(emotion)}
                >
                  <Text
                    style={[
                      styles.emotionText,
                      selectedEmotions.includes(emotion) && styles.selectedEmotionText
                    ]}
                  >
                    {emotion}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.sectionTitle}>Can you describe your feelings today?</Text>
            <TextInput
              style={styles.journalInput}
              placeholder="Type here..."
              value={journalEntry}
              onChangeText={setJournalEntry}
              multiline
              textAlignVertical="top"
            />

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={styles.backButtonFull}
                onPress={() => router.back()}
              >
                <Text style={styles.backButtonFullText}>Back</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleSave}
              >
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>

          <Modal
            visible={showModal}
            transparent={true}
            animationType="none"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalDate}>{formatDisplayDate(date)}</Text>
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => setShowModal(false)}
                  >
                    <Text style={styles.closeButtonText}>×</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.moodSummary}>
                  <Text style={styles.moodSummaryTitle}>Your Mood</Text>
                  {modalContent.mood && (
                    <Text style={styles.moodSummaryText}>
                      {modalContent.mood.label} {modalContent.mood.emoji}
                    </Text>
                  )}
                  {modalContent.entry && (
                    <Text style={styles.moodSummaryEntry}>{modalContent.entry}</Text>
                  )}
                </View>

                <View style={styles.recommendations}>
                  <Text style={styles.recommendationsTitle}>Recommendations</Text>
                  {modalContent.recommendations && modalContent.recommendations.map((rec, index) => (
                    <Text key={index} style={styles.recommendationItem}>• {rec}</Text>
                  ))}
                </View>

                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => setShowModal(false)}
                >
                  <Text style={styles.editButtonText}>Edit Entry</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
          <BottomNavigation />
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default MoodJournal;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#6B9142',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 30,
  },
  content: {
    padding: 20,
    backgroundColor: '#F5F9EE',
    paddingBottom: 80, // Space for bottom navigation
  },
  sectionTitle: {
    fontSize: 16,
    color: '#6B9142',
    marginBottom: 10,
  },
  moodContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  moodOption: {
    width: 60,
    height: 60,
    borderRadius: 10,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedMoodOption: {
    backgroundColor: '#6B9142',
    borderWidth: 0,
  },
  moodEmoji: {
    fontSize: 28,
  },
  emotionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  emotionChip: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    margin: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  selectedEmotionChip: {
    backgroundColor: '#6B9142',
  },
  emotionText: {
    color: '#666666',
    fontSize: 13,
  },
  selectedEmotionText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  journalInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 15,
    height: 120,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 80, // Add space for bottom navigation
  },
  backButtonFull: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#6B9142',
    marginRight: 10,
  },
  backButtonFullText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 12,
    alignItems: 'center',
    marginLeft: 10,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    width: '90%',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalDate: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  closeButtonText: {
    fontSize: 20,
    color: '#6B9142',
    fontWeight: 'bold',
  },
  moodSummary: {
    backgroundColor: '#F5F5F5',
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  moodSummaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 5,
  },
  moodSummaryText: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 10,
  },
  moodSummaryEntry: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
  },
  recommendations: {
    marginBottom: 15,
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
  },
  recommendationItem: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 5,
  },
  editButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 30,
    paddingVertical: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  editButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
});
