import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  FlatList
} from 'react-native';
import { useState } from 'react';
import { useRouter } from 'expo-router';
import BottomNavigation from './components/BottomNavigation';

const MentalAssessment = () => {
  const router = useRouter();

  const assessments = [
    {
      id: 'dass',
      title: 'DASS (Depression Anxiety Stress Scales)',
      description: 'A comprehensive assessment that measures three related negative emotional states of depression, anxiety, and stress. This test helps identify the severity of these core symptoms.',
      questions: 21,
      icon: 'D',
      color: '#6B9142'
    },
    {
      id: 'pss',
      title: 'PSS (Perceived Stress Scale)',
      description: 'A widely used psychological instrument that measures your perception of stress. It assesses how unpredictable, uncontrollable, and overloaded you find your life.',
      questions: 10,
      icon: 'P',
      color: '#6B9142'
    }
  ];

  const handleAssessmentSelect = (assessment) => {
    router.push({
      pathname: '/assessment-questions',
      params: { id: assessment.id }
    });
  };

  const renderAssessmentItem = ({ item }) => (
    <TouchableOpacity
      style={styles.assessmentCard}
      onPress={() => handleAssessmentSelect(item)}
    >
      <View style={[styles.assessmentIcon, { backgroundColor: item.color }]}>
        <Text style={styles.assessmentIconText}>{item.icon}</Text>
      </View>
      <View style={styles.assessmentInfo}>
        <Text style={styles.assessmentTitle}>{item.title}</Text>
        <Text style={styles.assessmentDescription}>{item.description}</Text>
        <View style={styles.assessmentMeta}>
          <Text style={styles.assessmentQuestions}>{item.questions} questions</Text>
          <TouchableOpacity
            style={styles.takeTestButton}
            onPress={() => handleAssessmentSelect(item)}
          >
            <Text style={styles.takeTestButtonText}>Take Test</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Mental Health Assessment</Text>
      </View>

      <Text style={styles.subtitle}>
        Please select which assessment you would like to take today
      </Text>

      <FlatList
        data={assessments}
        renderItem={renderAssessmentItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.assessmentList}
      />

      <BottomNavigation />
    </SafeAreaView>
  );
};

export default MentalAssessment;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#6B9142',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 30,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginVertical: 15,
    paddingHorizontal: 20,
  },
  assessmentList: {
    padding: 20,
    paddingBottom: 80, // Space for bottom navigation
  },
  assessmentCard: {
    flexDirection: 'row',
    backgroundColor: '#F5F9EE',
    borderRadius: 20,
    padding: 18,
    marginBottom: 18,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  assessmentIcon: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: '#6B9142',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 18,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  assessmentIconText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  assessmentInfo: {
    flex: 1,
  },
  assessmentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 5,
  },
  assessmentDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 10,
  },
  assessmentMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  assessmentQuestions: {
    fontSize: 12,
    color: '#999999',
  },
  takeTestButton: {
    backgroundColor: '#6B9142',
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  takeTestButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
});
