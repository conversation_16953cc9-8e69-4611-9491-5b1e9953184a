{"expo": {"scheme": "capstone", "name": "capstone", "slug": "capstone", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "androidStatusBar": {"barStyle": "dark-content", "backgroundColor": "transparent", "translucent": true}, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"UIViewControllerBasedStatusBarAppearance": true, "UIStatusBarStyle": "UIStatusBarStyleDarkContent"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router"]}}