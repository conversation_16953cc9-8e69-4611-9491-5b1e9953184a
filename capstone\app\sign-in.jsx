import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Alert,
  BackHandler
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useUser } from './context/UserContext';

const SignIn = () => {
  const router = useRouter();
  const { updateUserData } = useUser();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Handle back button press
  useEffect(() => {
    // Custom back button handler
    const handleBackPress = () => {
      // Navigate to welcome screen
      router.push('/');
      return true; // Prevent default behavior
    };

    // Add event listener for hardware back button (Android)
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    }

    // Cleanup function
    return () => {
      if (Platform.OS === 'android') {
        BackHandler.removeEventListener('hardwareBackPress', handleBackPress);
      }
    };
  }, [router]);

  // Mock function to check if user exists (in a real app, this would be an API call)
  const checkUserExists = (userEmail) => {
    // For demo purposes, we'll consider any email with "existing" to be an existing user
    const isExistingUser = userEmail.includes('existing');

    // In a real app, you would validate credentials against your backend
    return {
      exists: isExistingUser,
      userData: isExistingUser ? {
        firstName: "John",
        middleName: "",
        lastName: "Doe",
        email: userEmail,
        controlNumber: "MH-2023-45678",
        phone: "************",
        age: 30,
        gender: "Male",
        civilStatus: "Single",
        birthdate: "1993-05-15",
        birthplace: "New York",
        religion: "None",
        address: "123 Main St, Anytown, USA",
        // Ensure existing users are marked as verified and have complete profile
        emailVerified: true,
        hasCompletedProfile: true
      } : null
    };
  };

  const handleSignIn = () => {
    if (email && password) {
      setIsLoading(true);

      // Check if user exists (mock function)
      const { exists, userData: existingUserData } = checkUserExists(email);

      if (exists) {
        // User exists, update context with user data and use a callback to ensure
        // the context is updated before navigation occurs
        updateUserData({
          ...existingUserData,
          emailVerified: true, // Explicitly set to true to ensure verification is bypassed
          hasCompletedProfile: true // Flag to indicate user has completed their profile
        }, () => {
          // Log the action for debugging
          console.log("Existing user signed in, bypassing verification and personal info");

          // Navigate directly to dashboard - use replace to prevent going back
          router.replace('/dashboard');
          setIsLoading(false);
        });
      } else {
        // User doesn't exist, show error
        setIsLoading(false);
        Alert.alert(
          "Account Not Found", 
          "No account found with these credentials. Please check your email and password or create a new account.",
          [{ text: "OK" }]
        );
      }
    } else {
      // Show alert for empty fields
      Alert.alert("Error", "Please fill in all fields");
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.push('/')}
            >
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.logoContainer}>
            {/* Logo placeholder - you'll add the actual image later */}
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>mentalease</Text>
            </View>
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.title}>Sign In</Text>
            <Text style={styles.subtitle}>
              Please enter your credentials to sign in to your account
            </Text>
            
            <Text style={styles.label}>Email Address</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your email address"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              returnKeyType="next"
            />

            <Text style={styles.label}>Password</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              returnKeyType="done"
              onSubmitEditing={Keyboard.dismiss}
            />

            <TouchableOpacity
              style={[styles.signInButton, isLoading && styles.signInButtonDisabled]}
              onPress={() => {
                Keyboard.dismiss();
                handleSignIn();
              }}
              disabled={isLoading}
            >
              <Text style={styles.signInButtonText}>
                {isLoading ? "Signing in..." : "Sign in"}
              </Text>
            </TouchableOpacity>

            <Text style={styles.hintText}>
              Hint: Use any email with "existing" in it (e.g., <EMAIL>)
              to simulate an existing user login.
            </Text>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default SignIn;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  logoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#F5F9EE',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    color: '#6B9142',
    fontSize: 14,
    fontWeight: 'bold',
  },
  formContainer: {
    backgroundColor: '#F5F9EE',
    borderRadius: 15,
    margin: 20,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  label: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginBottom: 20,
    fontSize: 16,
  },
  signInButton: {
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  signInButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  signInButtonDisabled: {
    backgroundColor: '#A9C795',
    opacity: 0.7,
  },
  hintText: {
    marginTop: 15,
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
