import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter, usePathname } from 'expo-router';

const BottomNavigation = () => {
  const router = useRouter();
  const pathname = usePathname();

  // Use replace instead of push for instant navigation without animation
  const navigateTo = (path) => {
    // Only navigate if we're not already on this path
    if (pathname !== path) {
      router.replace(path);
    }
  };

  // Check if the current path is in the appointments section
  const isAppointmentsSection = [
    '/appointments',
    '/schedule-appointment',
    '/reschedule-appointment'
  ].includes(pathname);

  // Check if we're on the dashboard (either version)
  const isDashboard = ['/dashboard', '/dashboard-fixed'].includes(pathname);

  return (
    <View style={styles.bottomNav}>
      <TouchableOpacity
        style={[styles.navItem, isDashboard && styles.activeNavItem]}
        onPress={() => navigateTo('/dashboard')}
      >
        <View style={styles.navIconContainer}>
          <Text style={styles.navIcon}>🏠</Text>
        </View>
        <Text style={[styles.navText, isDashboard && styles.activeNavText]}>Home</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.navItem, pathname === '/mood-journal' && styles.activeNavItem]}
        onPress={() => navigateTo('/mood-journal')}
      >
        <View style={styles.navIconContainer}>
          <Text style={styles.navIcon}>📝</Text>
        </View>
        <Text style={[styles.navText, pathname === '/mood-journal' && styles.activeNavText]}>Journal</Text>
      </TouchableOpacity>

      {/* Center Chatbot Button */}
      <TouchableOpacity
        style={styles.centerButton}
        onPress={() => navigateTo('/ai-chatbot')}
      >
        <View style={styles.centerButtonInner}>
          <Text style={styles.centerButtonIcon}>💬</Text>
        </View>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.navItem, isAppointmentsSection && styles.activeNavItem]}
        onPress={() => navigateTo('/appointments')}
      >
        <View style={styles.navIconContainer}>
          <Text style={styles.navIcon}>📅</Text>
        </View>
        <Text style={[styles.navText, isAppointmentsSection && styles.activeNavText]}>Appointments</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.navItem, pathname === '/mental-assessment' && styles.activeNavItem]}
        onPress={() => navigateTo('/mental-assessment')}
      >
        <View style={styles.navIconContainer}>
          <Text style={styles.navIcon}>🧠</Text>
        </View>
        <Text style={[styles.navText, pathname === '/mental-assessment' && styles.activeNavText]}>Assessment</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 70,
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 10,
    paddingHorizontal: 10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  navItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
  },
  activeNavItem: {
    transform: [{ scale: 1.05 }],
  },
  navIconContainer: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    backgroundColor: '#F8F8F8',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  navIcon: {
    fontSize: 24,
  },
  navText: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
    fontWeight: '500',
  },
  activeNavText: {
    color: '#6B9142',
    fontWeight: 'bold',
  },
  centerButton: {
    width: 64,
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -32,
  },
  centerButtonInner: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#6B9142',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 8,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  centerButtonIcon: {
    fontSize: 26,
    color: '#FFFFFF',
  },
});

export default BottomNavigation;
