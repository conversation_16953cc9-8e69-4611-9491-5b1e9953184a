import { Stack, Slot } from 'expo-router';
import { useCallback } from 'react';
import { View } from 'react-native';
import { useFonts } from 'expo-font';
// Remove SplashScreen import as it's causing issues
// import * as SplashScreen from 'expo-splash-screen';
import CustomSlot from './components/Slot';
import { UserProvider } from './context/UserContext';
import { AppointmentProvider } from './context/AppointmentContext';

// Comment out SplashScreen usage
// SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    // You can add custom fonts here if needed
    // 'CustomFont-Regular': require('../assets/fonts/CustomFont-Regular.ttf'),
  });

  // Simplified onLayoutRootView function without SplashScreen
  const onLayoutRootView = useCallback(() => {
    // No need to do anything here now
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    return null;
  }

  // Using a conditional to demonstrate different navigation patterns
  const useStackNavigation = true;

  return (
    <UserProvider>
      <AppointmentProvider>
        <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
        {useStackNavigation ? (
          // Stack navigation pattern
          <Stack
            screenOptions={{
              headerStyle: {
                backgroundColor: '#ffffff',
              },
              headerShadowVisible: false,
              headerTintColor: '#6B9142', // Green color from the design
              headerTitleStyle: {
                fontWeight: '600',
                color: '#6B9142',
              },
              contentStyle: {
                backgroundColor: '#ffffff',
              },
              animation: 'none', // No animation for instant navigation
              animationDuration: 0, // No animation duration
            }}
          >
          <Stack.Screen
            name="onboarding"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="index"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="consent"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="sign-in"
            options={{
              headerShown: false, // Hide header since we added our own back button
            }}
          />
          <Stack.Screen
            name="create-account"
            options={{
              headerShown: false, // Hide header since we added our own back button
            }}
          />
          <Stack.Screen
            name="email-verification"
            options={{
              headerShown: false, // Hide header since we added our own back button
            }}
          />
          <Stack.Screen
            name="mood-tracker"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="mood-journal"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="mental-assessment"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="assessment-questions"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="assessment-results"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="ai-chatbot"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="online-consultation"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="user-profile"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="dashboard"
            options={{
              headerShown: false,
              gestureEnabled: false, // Disable swipe back gesture
            }}
          />
          <Stack.Screen
            name="dashboard-fixed"
            options={{
              headerShown: false,
              gestureEnabled: false, // Disable swipe back gesture
            }}
          />
          <Stack.Screen
            name="payment-details"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="customer-support"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="inquiries"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="appointments"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="schedule-appointment"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="reschedule-appointment"
            options={{
              headerShown: false,
            }}
          />
          {/* Example of using components folder */}
          <Stack.Screen
            name="components"
            options={{
              headerShown: false,
            }}
          />
        </Stack>
      ) : (
        // Using Slot for more flexible layouts
        <CustomSlot>
          <Slot />
        </CustomSlot>
      )}
    </View>
      </AppointmentProvider>
    </UserProvider>
  );
}
