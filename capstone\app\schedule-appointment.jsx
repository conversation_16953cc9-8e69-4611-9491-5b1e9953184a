import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  TextInput,
  Modal,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { useState } from 'react';
import { useRouter } from 'expo-router';
import { useAppointment } from './context/AppointmentContext';
import { getDates, getTimeSlots, formatDate } from './models/AppointmentModel';
import CustomStatusBar from './components/CustomStatusBar';

const ScheduleAppointment = () => {
  const router = useRouter();
  const {
    therapists,
    bookAppointment,
    isTimeSlotAvailable
  } = useAppointment();

  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [selectedTherapist, setSelectedTherapist] = useState(null);
  const [notes, setNotes] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const handleBookAppointment = () => {
    if (selectedDate && selectedTime && selectedTherapist) {
      setShowConfirmModal(true);
    }
  };

  const confirmAppointment = () => {
    // Book the appointment using the controller
    try {
      bookAppointment(
        selectedTherapist.id,
        selectedDate,
        selectedTime,
        notes
      );

      setShowConfirmModal(false);
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error booking appointment:', error);
      // Handle error (show alert, etc.)
    }
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    router.replace('/appointments');
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.container}>
          <CustomStatusBar backgroundColor="transparent" barStyle="dark-content" />

          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Schedule Appointment</Text>
          </View>

          <ScrollView contentContainerStyle={styles.content}>
            <Text style={styles.sectionTitle}>Select a Therapist</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.therapistsContainer}
            >
              {therapists.map((therapist) => (
                <TouchableOpacity
                  key={therapist.id}
                  style={[
                    styles.therapistCard,
                    selectedTherapist?.id === therapist.id && styles.selectedTherapistCard,
                    !therapist.available && styles.unavailableTherapistCard
                  ]}
                  onPress={() => therapist.available && setSelectedTherapist(therapist)}
                  disabled={!therapist.available}
                >
                  <Text style={styles.therapistImage}>{therapist.image}</Text>
                  <Text style={styles.therapistName}>{therapist.name}</Text>
                  <Text style={styles.therapistSpecialty}>{therapist.specialty}</Text>
                  <View style={styles.therapistInfoRow}>
                    <Text style={styles.therapistExperience}>{therapist.experience}</Text>
                    <Text style={styles.therapistRating}>⭐ {therapist.rating}</Text>
                  </View>
                  {!therapist.available && (
                    <View style={styles.unavailableOverlay}>
                      <Text style={styles.unavailableText}>Unavailable</Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a Date</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.datesContainer}
            >
              {getDates().map((date, index) => {
                const formattedDate = formatDate(date);
                const dateString = date.toISOString().split('T')[0];
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.dateCard,
                      selectedDate === dateString && styles.selectedDateCard
                    ]}
                    onPress={() => setSelectedDate(dateString)}
                  >
                    <Text style={styles.dateDay}>{formattedDate.day}</Text>
                    <Text style={styles.dateNumber}>{formattedDate.date}</Text>
                    <Text style={styles.dateMonth}>{formattedDate.month}</Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a Time</Text>
            <View style={styles.timeContainer}>
              {getTimeSlots().map((time, index) => {
                const available = selectedDate ? isTimeSlotAvailable(selectedDate, time) : true;
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.timeCard,
                      selectedTime === time && styles.selectedTimeCard,
                      !available && styles.unavailableTimeCard
                    ]}
                    onPress={() => available && setSelectedTime(time)}
                    disabled={!available}
                  >
                    <Text
                      style={[
                        styles.timeText,
                        selectedTime === time && styles.selectedTimeText,
                        !available && styles.unavailableTimeText
                      ]}
                    >
                      {time}
                    </Text>
                    {!available && (
                      <Text style={styles.bookedText}>Booked</Text>
                    )}
                  </TouchableOpacity>
                );
              })}
            </View>

            <Text style={styles.sectionTitle}>Additional Notes</Text>
            <TextInput
              style={styles.notesInput}
              placeholder="Add any notes for your therapist..."
              value={notes}
              onChangeText={setNotes}
              multiline
              textAlignVertical="top"
            />

            <TouchableOpacity
              style={[
                styles.bookButton,
                (!selectedDate || !selectedTime || !selectedTherapist) && styles.disabledButton
              ]}
              onPress={handleBookAppointment}
              disabled={!selectedDate || !selectedTime || !selectedTherapist}
            >
              <Text style={styles.bookButtonText}>Book Appointment</Text>
            </TouchableOpacity>
          </ScrollView>

          {/* Confirmation Modal */}
          <Modal
            visible={showConfirmModal}
            transparent={true}
            animationType="none"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Confirm Appointment</Text>

                {selectedTherapist && (
                  <View style={styles.confirmTherapist}>
                    <Text style={styles.confirmTherapistImage}>{selectedTherapist.image}</Text>
                    <View style={styles.confirmTherapistInfo}>
                      <Text style={styles.confirmTherapistName}>{selectedTherapist.name}</Text>
                      <Text style={styles.confirmTherapistSpecialty}>{selectedTherapist.specialty}</Text>
                    </View>
                  </View>
                )}

                {selectedDate && selectedTime && (
                  <View style={styles.confirmDateTime}>
                    <Text style={styles.confirmDateTimeText}>
                      {new Date(selectedDate).toLocaleDateString('en-US', {
                        weekday: 'short',
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </Text>
                    <Text style={styles.confirmDateTimeText}>at {selectedTime}</Text>
                  </View>
                )}

                <View style={styles.confirmButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowConfirmModal(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={confirmAppointment}
                  >
                    <Text style={styles.confirmButtonText}>Confirm</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Success Modal */}
          <Modal
            visible={showSuccessModal}
            transparent={true}
            animationType="none"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.successIcon}>✅</Text>
                <Text style={styles.successTitle}>Appointment Booked!</Text>
                <Text style={styles.successMessage}>
                  Your appointment has been successfully scheduled. You will receive a confirmation email shortly.
                </Text>

                <TouchableOpacity
                  style={styles.doneButton}
                  onPress={closeSuccessModal}
                >
                  <Text style={styles.doneButtonText}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 10 : 10,
    paddingBottom: 10,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: '#6B9142',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginLeft: 10,
  },
  content: {
    padding: 16,
    paddingBottom: 40,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  therapistsContainer: {
    paddingBottom: 10,
  },
  therapistCard: {
    width: 160,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedTherapistCard: {
    borderColor: '#6B9142',
    borderWidth: 2,
  },
  unavailableTherapistCard: {
    opacity: 0.6,
  },
  therapistImage: {
    fontSize: 40,
    marginBottom: 10,
    alignSelf: 'center',
  },
  therapistName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  therapistSpecialty: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 8,
  },
  therapistInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  therapistExperience: {
    fontSize: 11,
    color: '#888888',
  },
  therapistRating: {
    fontSize: 11,
    color: '#FF9500',
    fontWeight: 'bold',
  },
  unavailableOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unavailableText: {
    color: '#FF3B30',
    fontWeight: 'bold',
    fontSize: 14,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
  },
  datesContainer: {
    paddingBottom: 10,
  },
  dateCard: {
    width: 70,
    height: 90,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedDateCard: {
    backgroundColor: '#6B9142',
  },
  dateDay: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  dateNumber: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  dateMonth: {
    fontSize: 14,
    color: '#666666',
  },
  timeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timeCard: {
    width: '30%',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 12,
    marginBottom: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedTimeCard: {
    backgroundColor: '#6B9142',
  },
  unavailableTimeCard: {
    backgroundColor: '#F5F5F5',
  },
  timeText: {
    fontSize: 14,
    color: '#333333',
  },
  selectedTimeText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  unavailableTimeText: {
    color: '#999999',
  },
  bookedText: {
    fontSize: 10,
    color: '#FF3B30',
    marginTop: 4,
  },
  notesInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    height: 120,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  bookButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: '#6B9142',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
    shadowColor: '#999999',
  },
  bookButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 340,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 20,
  },
  confirmTherapist: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  confirmTherapistImage: {
    fontSize: 40,
    marginRight: 15,
  },
  confirmTherapistInfo: {
    flex: 1,
  },
  confirmTherapistName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  confirmTherapistSpecialty: {
    fontSize: 14,
    color: '#666666',
  },
  confirmDateTime: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    marginBottom: 20,
    alignItems: 'center',
  },
  confirmDateTimeText: {
    fontSize: 16,
    color: '#333333',
    marginBottom: 4,
  },
  confirmButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginLeft: 10,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  successIcon: {
    fontSize: 50,
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  successMessage: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  doneButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 40,
    alignItems: 'center',
  },
  doneButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ScheduleAppointment;
