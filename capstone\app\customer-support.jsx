import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import BottomNavigation from './components/BottomNavigation';
import { useUser } from './context/UserContext';

const CustomerSupport = () => {
  const router = useRouter();
  const { userData } = useUser();
  const [activeTab, setActiveTab] = useState('contact');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [selectedIssue, setSelectedIssue] = useState(null);

  const issueTypes = [
    { id: 1, title: 'Technical Issue', icon: 'bug' },
    { id: 2, title: 'Account Problem', icon: 'person-circle' },
    { id: 3, title: 'Billing Question', icon: 'card' },
    { id: 4, title: 'Feature Request', icon: 'bulb' },
    { id: 5, title: 'Other', icon: 'help-circle' },
  ];

  const faqItems = [
    {
      id: 1,
      question: 'How do I reset my password?',
      answer: 'To reset your password, go to the login screen and tap on "Forgot Password". Follow the instructions sent to your email to create a new password.'
    },
    {
      id: 2,
      question: 'How can I schedule a consultation?',
      answer: 'You can schedule a consultation by going to the Professional Consultation section on the dashboard. Select your preferred date, time, and professional to book your appointment.'
    },
    {
      id: 3,
      question: 'Is my data secure and private?',
      answer: 'Yes, we take your privacy seriously. All your data is encrypted and stored securely. We do not share your personal information with third parties without your consent.'
    },
    {
      id: 4,
      question: 'How do I update my personal information?',
      answer: 'You can update your personal information by going to your profile page. Tap on the edit button next to the information you want to update.'
    },
    {
      id: 5,
      question: 'Can I download my assessment results?',
      answer: 'Yes, after completing an assessment, you can download your results by going to the assessment results page and tapping on the download button.'
    },
  ];

  const [expandedFaq, setExpandedFaq] = useState(null);

  // Dismiss keyboard when tapping outside input fields
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const handleSubmit = () => {
    if (!selectedIssue) {
      Alert.alert('Error', 'Please select an issue type');
      return;
    }
    
    if (!subject.trim()) {
      Alert.alert('Error', 'Please enter a subject');
      return;
    }
    
    if (!message.trim()) {
      Alert.alert('Error', 'Please enter your message');
      return;
    }
    
    // Submit support request
    Alert.alert(
      'Support Request Submitted',
      'Thank you for contacting us. We will get back to you within 24 hours.',
      [{ text: 'OK', onPress: () => {
        setSelectedIssue(null);
        setSubject('');
        setMessage('');
      }}]
    );
  };

  const toggleFaq = (id) => {
    if (expandedFaq === id) {
      setExpandedFaq(null);
    } else {
      setExpandedFaq(id);
    }
  };

  const renderContactForm = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Contact Support</Text>
      
      <Text style={styles.formLabel}>Select Issue Type</Text>
      <View style={styles.issueTypesContainer}>
        {issueTypes.map((issue) => (
          <TouchableOpacity
            key={issue.id}
            style={[
              styles.issueTypeButton,
              selectedIssue === issue.id && styles.selectedIssueButton
            ]}
            onPress={() => setSelectedIssue(issue.id)}
          >
            <Ionicons
              name={issue.icon}
              size={22}
              color={selectedIssue === issue.id ? '#FFFFFF' : '#FF9800'}
              style={styles.issueIcon}
            />
            <Text
              style={[
                styles.issueTypeText,
                selectedIssue === issue.id && styles.selectedIssueText
              ]}
            >
              {issue.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      
      <Text style={styles.formLabel}>Subject</Text>
      <TextInput
        style={styles.input}
        value={subject}
        onChangeText={setSubject}
        placeholder="Enter subject"
        placeholderTextColor="#999999"
      />
      
      <Text style={styles.formLabel}>Message</Text>
      <TextInput
        style={styles.messageInput}
        value={message}
        onChangeText={setMessage}
        placeholder="Describe your issue in detail"
        placeholderTextColor="#999999"
        multiline
        textAlignVertical="top"
      />
      
      <TouchableOpacity
        style={styles.submitButton}
        onPress={handleSubmit}
      >
        <Text style={styles.submitButtonText}>Submit Request</Text>
      </TouchableOpacity>
    </View>
  );

  const renderFaq = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
      
      {faqItems.map((item) => (
        <TouchableOpacity
          key={item.id}
          style={styles.faqItem}
          onPress={() => toggleFaq(item.id)}
        >
          <View style={styles.faqHeader}>
            <Text style={styles.faqQuestion}>{item.question}</Text>
            <Ionicons
              name={expandedFaq === item.id ? 'chevron-up' : 'chevron-down'}
              size={20}
              color="#FF9800"
            />
          </View>
          
          {expandedFaq === item.id && (
            <Text style={styles.faqAnswer}>{item.answer}</Text>
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <SafeAreaView style={styles.container}>
        <StatusBar
          translucent={true}
          backgroundColor="transparent"
          barStyle="light-content"
        />
        
        {/* Header */}
        <LinearGradient
          colors={['#FF9800', '#F57C00']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Customer Support</Text>
            <View style={styles.placeholderButton} />
          </View>
        </LinearGradient>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'contact' && styles.activeTab]}
            onPress={() => setActiveTab('contact')}
          >
            <Text style={[styles.tabText, activeTab === 'contact' && styles.activeTabText]}>
              Contact Us
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'faq' && styles.activeTab]}
            onPress={() => setActiveTab('faq')}
          >
            <Text style={[styles.tabText, activeTab === 'faq' && styles.activeTabText]}>
              FAQ
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}
        >
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {activeTab === 'contact' && renderContactForm()}
            {activeTab === 'faq' && renderFaq()}
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 15,
    paddingBottom: 15,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  placeholderButton: {
    width: 40,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 5,
    paddingHorizontal: 10,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
  },
  tabText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#FF9800',
    fontWeight: 'bold',
  },
  tabContent: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    marginTop: 16,
  },
  issueTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  issueTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedIssueButton: {
    backgroundColor: '#FF9800',
  },
  issueIcon: {
    marginRight: 6,
  },
  issueTypeText: {
    fontSize: 13,
    color: '#FF9800',
    fontWeight: '500',
  },
  selectedIssueText: {
    color: '#FFFFFF',
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 15,
    color: '#333333',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  messageInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 15,
    color: '#333333',
    height: 150,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  submitButton: {
    backgroundColor: '#FF9800',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 24,
    shadowColor: '#FF9800',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  faqItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  faqQuestion: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    paddingRight: 8,
  },
  faqAnswer: {
    fontSize: 14,
    color: '#666666',
    marginTop: 12,
    lineHeight: 20,
  },
});

export default CustomerSupport;
