import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Modal,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useAppointment } from './context/AppointmentContext';
import { getDates, getTimeSlots, formatDate } from './models/AppointmentModel';
import CustomStatusBar from './components/CustomStatusBar';

const RescheduleAppointment = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const appointmentId = params.id;
  
  const {
    appointments,
    rescheduleAppointment,
    isTimeSlotAvailable,
    getTherapistById
  } = useAppointment();

  // Find the appointment to reschedule
  const [appointment, setAppointment] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // Load the appointment details
  useEffect(() => {
    if (appointmentId) {
      const foundAppointment = appointments.find(a => a.id === appointmentId);
      if (foundAppointment) {
        setAppointment(foundAppointment);
        setSelectedDate(foundAppointment.date);
        setSelectedTime(foundAppointment.time);
      } else {
        // Handle appointment not found
        alert('Appointment not found');
        router.replace('/appointments');
      }
    } else {
      // No appointment ID provided
      alert('No appointment specified');
      router.replace('/appointments');
    }
  }, [appointmentId, appointments]);

  const handleRescheduleAppointment = () => {
    if (selectedDate && selectedTime) {
      // Check if the date or time has actually changed
      if (selectedDate === appointment.date && selectedTime === appointment.time) {
        alert('Please select a different date or time to reschedule');
        return;
      }
      
      // Check if the selected time slot is available
      if (!isTimeSlotAvailable(selectedDate, selectedTime, appointmentId)) {
        alert('This time slot is not available');
        return;
      }
      
      setShowConfirmModal(true);
    }
  };

  const confirmReschedule = () => {
    try {
      // Call the reschedule function from the controller
      rescheduleAppointment(appointmentId, selectedDate, selectedTime);
      
      setShowConfirmModal(false);
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      alert('Failed to reschedule appointment');
    }
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    router.replace('/appointments');
  };

  // If appointment is not loaded yet, show loading state
  if (!appointment) {
    return (
      <SafeAreaView style={styles.container}>
        <CustomStatusBar backgroundColor="transparent" barStyle="dark-content" />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading appointment details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Get therapist details
  const therapist = getTherapistById(appointment.therapistId);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.container}>
          <CustomStatusBar backgroundColor="transparent" barStyle="dark-content" />

          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Reschedule Appointment</Text>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <View style={styles.appointmentCard}>
              <View style={styles.therapistInfo}>
                <Text style={styles.therapistImage}>{therapist.image}</Text>
                <View>
                  <Text style={styles.therapistName}>{therapist.name}</Text>
                  <Text style={styles.therapistSpecialty}>{therapist.specialty}</Text>
                </View>
              </View>
              <Text style={styles.currentAppointmentText}>
                Current: {appointment.date} at {appointment.time}
              </Text>
            </View>

            <Text style={styles.sectionTitle}>Select a New Date</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.datesContainer}
            >
              {getDates().map((date, index) => {
                const formattedDate = formatDate(date);
                const dateString = date.toISOString().split('T')[0];
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.dateCard,
                      selectedDate === dateString && styles.selectedDateCard
                    ]}
                    onPress={() => setSelectedDate(dateString)}
                  >
                    <Text style={styles.dateDay}>{formattedDate.day}</Text>
                    <Text style={styles.dateNumber}>{formattedDate.date}</Text>
                    <Text style={styles.dateMonth}>{formattedDate.month}</Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a New Time</Text>
            <View style={styles.timeContainer}>
              {getTimeSlots().map((time, index) => {
                const available = isTimeSlotAvailable(selectedDate, time, appointmentId);
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.timeCard,
                      selectedTime === time && styles.selectedTimeCard,
                      !available && styles.unavailableTimeCard
                    ]}
                    onPress={() => available && setSelectedTime(time)}
                    disabled={!available}
                  >
                    <Text
                      style={[
                        styles.timeText,
                        selectedTime === time && styles.selectedTimeText,
                        !available && styles.unavailableTimeText
                      ]}
                    >
                      {time}
                    </Text>
                    {!available && (
                      <Text style={styles.bookedText}>Booked</Text>
                    )}
                  </TouchableOpacity>
                );
              })}
            </View>

            <TouchableOpacity
              style={[
                styles.rescheduleButton,
                (!selectedDate || !selectedTime) && styles.disabledButton
              ]}
              onPress={handleRescheduleAppointment}
              disabled={!selectedDate || !selectedTime}
            >
              <Text style={styles.rescheduleButtonText}>Reschedule Appointment</Text>
            </TouchableOpacity>
          </ScrollView>

          {/* Confirmation Modal */}
          <Modal
            visible={showConfirmModal}
            transparent={true}
            animationType="fade"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Confirm Reschedule</Text>
                <Text style={styles.modalText}>
                  Are you sure you want to reschedule your appointment with {therapist.name} to {selectedDate} at {selectedTime}?
                </Text>
                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={styles.modalCancelButton}
                    onPress={() => setShowConfirmModal(false)}
                  >
                    <Text style={styles.modalCancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.modalConfirmButton}
                    onPress={confirmReschedule}
                  >
                    <Text style={styles.modalConfirmButtonText}>Confirm</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Success Modal */}
          <Modal
            visible={showSuccessModal}
            transparent={true}
            animationType="fade"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.successIcon}>✅</Text>
                <Text style={styles.modalTitle}>Appointment Rescheduled</Text>
                <Text style={styles.modalText}>
                  Your appointment has been successfully rescheduled to {selectedDate} at {selectedTime}.
                </Text>
                <TouchableOpacity
                  style={styles.modalDoneButton}
                  onPress={closeSuccessModal}
                >
                  <Text style={styles.modalDoneButtonText}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default RescheduleAppointment;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B9142',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    fontSize: 16,
    color: '#6B9142',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
    marginRight: 30, // To offset the back button and center the title
  },
  content: {
    flex: 1,
    padding: 20,
  },
  appointmentCard: {
    backgroundColor: '#F5F9EE',
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
  },
  therapistInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  therapistImage: {
    fontSize: 30,
    marginRight: 10,
  },
  therapistName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  therapistSpecialty: {
    fontSize: 14,
    color: '#666666',
  },
  currentAppointmentText: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 10,
    marginTop: 20,
  },
  datesContainer: {
    paddingBottom: 10,
  },
  dateCard: {
    width: 70,
    height: 90,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    padding: 10,
  },
  selectedDateCard: {
    backgroundColor: '#6B9142',
  },
  dateDay: {
    fontSize: 14,
    color: '#666666',
  },
  dateNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginVertical: 5,
  },
  dateMonth: {
    fontSize: 14,
    color: '#666666',
  },
  timeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timeCard: {
    width: '30%',
    height: 50,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  selectedTimeCard: {
    backgroundColor: '#6B9142',
  },
  unavailableTimeCard: {
    backgroundColor: '#F5F5F5',
    opacity: 0.5,
  },
  timeText: {
    fontSize: 14,
    color: '#333333',
  },
  selectedTimeText: {
    color: '#FFFFFF',
  },
  unavailableTimeText: {
    color: '#999999',
  },
  bookedText: {
    fontSize: 12,
    color: '#999999',
  },
  rescheduleButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  rescheduleButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 10,
  },
  modalText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalCancelButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 12,
    width: '48%',
    alignItems: 'center',
  },
  modalCancelButtonText: {
    fontSize: 14,
    color: '#666666',
  },
  modalConfirmButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    padding: 12,
    width: '48%',
    alignItems: 'center',
  },
  modalConfirmButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  successIcon: {
    fontSize: 40,
    marginBottom: 10,
  },
  modalDoneButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    padding: 12,
    width: '100%',
    alignItems: 'center',
  },
  modalDoneButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});
